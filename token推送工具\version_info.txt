
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2, 0, 0, 0),
    prodvers=(2, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'CompanyName', u'kkk'),
            StringStruct(u'FileDescription', u'Cursor Token Pusher - 现代化Token推送工具'),
            StringStruct(u'FileVersion', u'2.0'),
            StringStruct(u'InternalName', u'CursorTokenPusher'),
            StringStruct(u'LegalCopyright', u'Copyright © 2024 kkk'),
            StringStruct(u'OriginalFilename', u'CursorTokenPusher.exe'),
            StringStruct(u'ProductName', u'Cursor Token Pusher'),
            StringStruct(u'ProductVersion', u'2.0'),
            StringStruct(u'Comments', u'联系邮箱: <EMAIL>')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
