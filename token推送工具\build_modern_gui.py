#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Token Pusher - 现代化GUI打包脚本
作者: kkk
邮箱: <EMAIL>
"""

import os
import sys
import subprocess
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

# 版本信息
VERSION = "2.0"
AUTHOR = "kkk"
EMAIL = "<EMAIL>"

def check_requirements():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        return False
    
    # 检查必要文件
    required_files = [
        "cursor_token_pusher_gui_modern.py",
        "cursor_token_pusher.py", 
        "cursor_config.py"
    ]
    
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            return False
    
    print("✅ 环境检查通过")
    return True

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def create_version_info():
    """创建版本信息文件"""
    version_info = f'''
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2, 0, 0, 0),
    prodvers=(2, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'CompanyName', u'{AUTHOR}'),
            StringStruct(u'FileDescription', u'Cursor Token Pusher - 现代化Token推送工具'),
            StringStruct(u'FileVersion', u'{VERSION}'),
            StringStruct(u'InternalName', u'CursorTokenPusher'),
            StringStruct(u'LegalCopyright', u'Copyright © 2024 {AUTHOR}'),
            StringStruct(u'OriginalFilename', u'CursorTokenPusher.exe'),
            StringStruct(u'ProductName', u'Cursor Token Pusher'),
            StringStruct(u'ProductVersion', u'{VERSION}'),
            StringStruct(u'Comments', u'联系邮箱: {EMAIL}')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''.format(AUTHOR=AUTHOR, VERSION=VERSION, EMAIL=EMAIL)
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ 版本信息文件已创建")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    try:
        # 使用简单的PyInstaller命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name=CursorTokenPusher",
            "--version-file=version_info.txt",
            "--add-data=cursor_config.py;.",
            "--exclude-module=matplotlib",
            "--exclude-module=numpy",
            "--exclude-module=pandas",
            "--exclude-module=scipy",
            "--exclude-module=PIL",
            "--exclude-module=cv2",
            "--exclude-module=torch",
            "--exclude-module=tensorflow",
            "cursor_token_pusher_gui_modern.py"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 可执行文件构建成功")
            return True
        else:
            print(f"❌ 构建失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
        return False

def create_release_package():
    """创建发布包"""
    print("📦 创建发布包...")
    
    # 创建发布目录
    release_dir = Path(f"CursorTokenPusher_v{VERSION}_Release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制可执行文件
    exe_source = Path("dist/CursorTokenPusher.exe")
    if exe_source.exists():
        shutil.copy2(exe_source, release_dir / "CursorTokenPusher.exe")
        
        # 获取文件大小
        size_mb = exe_source.stat().st_size / (1024 * 1024)
        print(f"📏 可执行文件大小: {size_mb:.1f} MB")
    else:
        print("❌ 未找到可执行文件")
        return False
    
    # 复制文档文件
    doc_files = [
        ("../使用说明_v2.md", "使用说明_v2.md"),
        ("../环境配置帮助.md", "环境配置帮助.md"),
        ("../用户需要提供的信息.md", "用户需要提供的信息.md")
    ]

    for src_file, dst_file in doc_files:
        if Path(src_file).exists():
            shutil.copy2(src_file, release_dir / dst_file)
            print(f"✅ 复制文档: {dst_file}")
        else:
            print(f"⚠️ 文档不存在: {src_file}")
    
    # 创建README
    readme_content = f"""
# Cursor Token Pusher v{VERSION}

## 🚀 简介
现代化的Cursor Token推送工具，支持一键推送WorkosCursorSessionToken到Cursor编辑器。

## 👨‍💻 作者信息
- **作者**: {AUTHOR}
- **邮箱**: {EMAIL}
- **版本**: v{VERSION}
- **构建时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🎯 使用方法
1. 双击运行 `CursorTokenPusher.exe`
2. 输入你的 WorkosCursorSessionToken
3. 配置Cursor路径（通常自动检测）
4. 点击"推送Token"按钮
5. 完成！

## ⚠️ 重要提示
**推送前请务必：**
1. 退出Cursor中的当前账号
2. 完全关闭Cursor程序

## 📖 详细说明
请查看以下文档：
- `使用说明_v2.md` - 详细使用指南
- `环境配置帮助.md` - 环境配置说明
- `用户需要提供的信息.md` - 快速入门指南

## 🔧 系统要求
- Windows 7 或更高版本
- 已安装Cursor编辑器

## 📞 技术支持
如遇问题请联系: {EMAIL}

---
© 2024 {AUTHOR}. All rights reserved.
"""
    
    with open(release_dir / "README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content.strip())
    
    # 创建ZIP包
    zip_name = f"CursorTokenPusher_v{VERSION}_Windows.zip"
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in release_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(release_dir)
                zipf.write(file_path, arcname)
    
    print(f"✅ 发布包已创建: {zip_name}")
    print(f"📁 发布目录: {release_dir}")
    
    return True

def cleanup():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    
    temp_files = [
        "version_info.txt",
        "build",
        "dist",
        "__pycache__"
    ]
    
    for item in temp_files:
        path = Path(item)
        if path.exists():
            if path.is_dir():
                shutil.rmtree(path)
            else:
                path.unlink()
    
    print("✅ 清理完成")

def main():
    """主函数"""
    print("=" * 60)
    print(f"🚀 Cursor Token Pusher v{VERSION} - 现代化GUI打包工具")
    print(f"👨‍💻 作者: {AUTHOR}")
    print(f"📧 邮箱: {EMAIL}")
    print("=" * 60)
    print()
    
    # 检查环境
    if not check_requirements():
        return
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return
    
    # 创建版本信息
    create_version_info()
    
    # 构建可执行文件
    if not build_executable():
        return
    
    # 创建发布包
    if not create_release_package():
        return
    
    print()
    print("🎉 打包完成！")
    print()
    print("📦 输出文件:")
    print(f"   - CursorTokenPusher_v{VERSION}_Windows.zip (完整发布包)")
    print(f"   - CursorTokenPusher_v{VERSION}_Release/ (发布目录)")
    print()
    print("💡 建议:")
    print("   1. 测试可执行文件是否正常工作")
    print("   2. 将ZIP文件分发给用户")
    print("   3. 提供README.txt中的使用说明")
    
    # 询问是否清理临时文件
    try:
        choice = input("\n🗑️ 是否清理临时文件? (y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            cleanup()
    except KeyboardInterrupt:
        print("\n")
    
    print("\n✨ 打包流程完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 打包被用户中断")
    except Exception as e:
        print(f"\n❌ 打包过程发生错误: {str(e)}")
