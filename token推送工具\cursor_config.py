#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor配置文件 - 自定义Cursor安装路径
"""

import os
import sys

# 自定义Cursor安装路径
CUSTOM_CURSOR_PATHS = {
    "win32": [
        "D:\\cursor\\Cursor.exe",  # 用户的实际安装路径
        # 可以添加更多自定义路径
    ],
    "darwin": [
        "/Applications/Cursor.app/Contents/MacOS/Cursor",
        # 可以添加更多自定义路径
    ],
    "linux": [
        "/usr/bin/cursor",
        "/usr/local/bin/cursor",
        "/opt/cursor/cursor",
        # 可以添加更多自定义路径
    ]
}

def get_cursor_path():
    """获取当前系统的Cursor路径"""
    platform = sys.platform
    paths = CUSTOM_CURSOR_PATHS.get(platform, [])
    
    for path in paths:
        if os.path.exists(path):
            return path
    
    return None

def add_cursor_path(path):
    """添加新的Cursor路径"""
    platform = sys.platform
    if platform in CUSTOM_CURSOR_PATHS:
        if path not in CUSTOM_CURSOR_PATHS[platform]:
            CUSTOM_CURSOR_PATHS[platform].insert(0, path)
            return True
    return False

if __name__ == "__main__":
    # 测试配置
    cursor_path = get_cursor_path()
    if cursor_path:
        print(f"✅ 找到Cursor: {cursor_path}")
    else:
        print("❌ 未找到Cursor安装路径")
        print("请检查配置或手动添加路径")
