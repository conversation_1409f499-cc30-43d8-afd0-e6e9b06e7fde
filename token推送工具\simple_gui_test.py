#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版GUI测试 - 只包含核心功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import sys
import os

# 导入核心功能
try:
    from cursor_token_pusher import CursorTokenPusher
    print("✅ 成功导入cursor_token_pusher")
except Exception as e:
    print(f"❌ 导入cursor_token_pusher失败: {e}")
    sys.exit(1)

class SimpleGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Cursor Token Pusher - 简化版")
        self.root.geometry("600x500")
        
        try:
            self.pusher = CursorTokenPusher()
            print("✅ CursorTokenPusher初始化成功")
        except Exception as e:
            print(f"❌ CursorTokenPusher初始化失败: {e}")
            messagebox.showerror("错误", f"初始化失败: {e}")
            return
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="🚀 Cursor Token Pusher", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Token输入
        token_frame = tk.Frame(main_frame)
        token_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(token_frame, text="WorkosCursorSessionToken:", font=("Arial", 10)).pack(anchor=tk.W)
        self.token_entry = tk.Entry(token_frame, font=("Arial", 10), show="*")
        self.token_entry.pack(fill=tk.X, pady=(5, 0))
        
        # Cursor路径
        path_frame = tk.Frame(main_frame)
        path_frame.pack(fill=tk.X, pady=(0, 15))
        
        tk.Label(path_frame, text="Cursor路径:", font=("Arial", 10)).pack(anchor=tk.W)
        
        path_input_frame = tk.Frame(path_frame)
        path_input_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.path_entry = tk.Entry(path_input_frame, font=("Arial", 10))
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        browse_btn = tk.Button(path_input_frame, text="浏览", command=self.browse_path)
        browse_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 预填充路径
        if self.pusher.cursor_exe_path:
            self.path_entry.insert(0, self.pusher.cursor_exe_path)
        
        # 按钮区域
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 推送按钮
        self.push_button = tk.Button(button_frame,
                                   text="🚀 推送 Token",
                                   command=self.push_token,
                                   font=("Arial", 12, "bold"),
                                   bg="#2563eb",
                                   fg="white",
                                   padx=30,
                                   pady=10)
        self.push_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 测试按钮
        test_btn = tk.Button(button_frame,
                           text="🔍 测试连接",
                           command=self.test_connection,
                           font=("Arial", 10),
                           padx=20,
                           pady=8)
        test_btn.pack(side=tk.LEFT)
        
        # 日志区域
        log_frame = tk.Frame(main_frame)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        tk.Label(log_frame, text="操作日志:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        
        self.log_text = scrolledtext.ScrolledText(log_frame,
                                                height=10,
                                                font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 添加欢迎消息
        self.log("🎉 欢迎使用 Cursor Token Pusher!")
        self.log("⚠️ 推送前请先退出Cursor账号并完全关闭Cursor!")
    
    def log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def browse_path(self):
        """浏览选择Cursor路径"""
        filename = filedialog.askopenfilename(
            title="选择Cursor.exe文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.path_entry.delete(0, tk.END)
            self.path_entry.insert(0, filename)
            self.log(f"📁 已选择Cursor路径: {filename}")
    
    def test_connection(self):
        """测试连接"""
        def test():
            self.log("🔍 正在测试Cursor连接...")
            if self.pusher.verify_token_installation():
                self.log("✅ Cursor连接正常")
                messagebox.showinfo("测试结果", "Cursor连接正常！")
            else:
                self.log("⚠️ Cursor连接异常或未配置Token")
                messagebox.showwarning("测试结果", "Cursor连接异常或未配置Token")
        
        threading.Thread(target=test, daemon=True).start()
    
    def push_token(self):
        """推送Token"""
        token = self.token_entry.get().strip()
        cursor_path = self.path_entry.get().strip()
        
        if not token:
            self.log("❌ 请输入WorkosCursorSessionToken")
            messagebox.showerror("错误", "请输入WorkosCursorSessionToken！")
            return
        
        # 确认操作
        if not messagebox.askyesno("确认", "确认推送Token吗？\n\n请确保已退出Cursor账号并关闭Cursor程序。"):
            return
        
        # 更新Cursor路径
        if cursor_path and os.path.exists(cursor_path):
            self.pusher.cursor_exe_path = cursor_path
        
        # 禁用按钮
        self.push_button.config(state="disabled", text="推送中...")
        
        def push():
            try:
                self.log("🚀 开始推送Token...")
                
                if self.pusher.push_token(token):
                    self.log("✅ Token推送成功！")
                    
                    if messagebox.askyesno("成功", "Token推送成功！\n\n是否立即启动Cursor？"):
                        self.log("🚀 正在启动Cursor...")
                        if self.pusher.launch_cursor():
                            self.log("✅ Cursor启动成功！")
                        else:
                            self.log("❌ Cursor启动失败，请手动启动")
                else:
                    self.log("❌ Token推送失败")
                    messagebox.showerror("错误", "Token推送失败！")
                    
            except Exception as e:
                self.log(f"❌ 发生错误: {str(e)}")
                messagebox.showerror("错误", f"发生错误：{str(e)}")
            finally:
                self.push_button.config(state="normal", text="🚀 推送 Token")
        
        threading.Thread(target=push, daemon=True).start()

def main():
    root = tk.Tk()
    app = SimpleGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
