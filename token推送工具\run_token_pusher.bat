@echo off
chcp 65001 >nul
title Cursor Token Pusher

echo ========================================
echo    Cursor Token Pusher - 快速推送工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM 检查文件是否存在
if not exist "cursor_token_pusher.py" (
    echo ❌ 错误: 未找到 cursor_token_pusher.py
    echo 请确保所有文件都在同一目录下
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 运行Python脚本
python cursor_token_pusher.py

REM 如果出错，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 程序执行出错
    echo.
)

pause
