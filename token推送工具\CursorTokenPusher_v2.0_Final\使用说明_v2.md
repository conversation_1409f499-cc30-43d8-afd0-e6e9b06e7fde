# Cursor Token Pusher - 使用说明 v2.0

## 🆕 新版本特性

### ✨ 主要改进
- ✅ **简化输入** - 只需要输入Token，移除了邮箱输入
- ✅ **路径配置** - 用户可以手动指定Cursor安装路径
- ✅ **智能检测** - 自动检测Cursor路径，支持手动选择
- ✅ **容错增强** - 更好的错误处理和用户提示

## 📋 使用步骤

### 1. 启动程序
```bash
# 现代版（推荐）
python cursor_token_pusher_gui_modern.py

# 基础版
python cursor_token_pusher_gui.py

# 命令行版
python push_token.py <your_token>
```

### 2. 输入Token
在"Token信息"区域输入你的 `WorkosCursorSessionToken`

### 3. 配置Cursor路径
在"Cursor路径配置"区域：
- **自动检测** - 点击"🔍 自动检测"按钮
- **手动选择** - 点击"📂 浏览"按钮选择Cursor.exe文件
- **直接输入** - 在输入框中直接输入完整路径

### 4. 推送Token
点击"🚀 推送Token"按钮开始推送

### 5. 启动Cursor
推送成功后选择是否自动启动Cursor

## 🔧 Cursor路径配置详解

### Windows系统常见路径
```
D:\cursor\Cursor.exe                                    # 自定义安装
C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe  # 用户安装
C:\Program Files\Cursor\Cursor.exe                     # 系统安装
C:\Program Files (x86)\Cursor\Cursor.exe               # 32位系统安装
```

### macOS系统路径
```
/Applications/Cursor.app/Contents/MacOS/Cursor
~/Applications/Cursor.app/Contents/MacOS/Cursor
```

### Linux系统路径
```
/usr/bin/cursor
/usr/local/bin/cursor
/opt/cursor/cursor
~/.local/bin/cursor
```

## 🎯 界面说明

### 现代版界面布局
```
┌─────────────────────────────────────────┐
│           🚀 Cursor Token Pusher        │
│              现代化Token推送工具          │
├─────────────────────────────────────────┤
│ ╭─ 📊 Cursor 状态 ─────────────────────╮ │
│ │ 数据库: ✅ 已找到                     │ │
│ │ 可执行文件: ✅ 已找到                 │ │
│ ╰───────────────────────────────────────╯ │
├─────────────────────────────────────────┤
│ ╭─ 🔑 Token 信息 ──────────────────────╮ │
│ │ WorkosCursorSessionToken:             │ │
│ │ [*********************] [👁]         │ │
│ ╰───────────────────────────────────────╯ │
├─────────────────────────────────────────┤
│ ╭─ 📁 Cursor 路径配置 ─────────────────╮ │
│ │ [D:\cursor\Cursor.exe] [📂] [🔍]     │ │
│ ╰───────────────────────────────────────╯ │
├─────────────────────────────────────────┤
│ [🚀 推送 Token] [💾 备份] [🔍 测试]     │
├─────────────────────────────────────────┤
│ ╭─ 📋 操作日志 ─────────────────────────╮ │
│ │ 🎉 欢迎使用 Cursor Token Pusher!     │ │
│ │ ✅ 成功消息 (绿色)                   │ │
│ │ ⚠️ 警告消息 (橙色)                   │ │
│ │ ❌ 错误消息 (红色)                   │ │
│ ╰───────────────────────────────────────╯ │
└─────────────────────────────────────────┘
```

## 🛠️ 功能按钮说明

### Token区域
- **👁 显示/隐藏** - 切换Token的显示状态

### 路径配置区域
- **🔍 自动检测** - 自动扫描系统中的Cursor安装路径
- **📂 浏览** - 打开文件选择对话框，手动选择Cursor可执行文件

### 操作按钮
- **🚀 推送Token** - 执行Token推送操作
- **💾 备份数据库** - 备份Cursor数据库文件
- **🔍 测试连接** - 测试Cursor连接状态

### 日志区域
- **🗑 清空日志** - 清空操作日志
- **ℹ 关于** - 显示程序信息和使用说明

## ⚠️ 注意事项

### 使用前准备
1. **关闭Cursor** - 推送前请确保Cursor完全关闭
2. **准备Token** - 确保有有效的WorkosCursorSessionToken
3. **确认路径** - 确保Cursor路径配置正确

### 常见问题解决

#### 问题1: 自动检测失败
```
解决方案:
1. 点击"📂 浏览"手动选择Cursor.exe文件
2. 或直接在输入框中输入完整路径
3. 确保路径格式正确（Windows使用反斜杠\）
```

#### 问题2: 推送失败
```
可能原因:
- Cursor正在运行（请完全关闭Cursor）
- 数据库文件被锁定
- 权限不足
- Token无效或已过期

解决方案:
1. 完全关闭Cursor进程
2. 以管理员身份运行程序
3. 检查Token是否正确
```

#### 问题3: 无法启动Cursor
```
可能原因:
- Cursor路径配置错误
- 文件不存在
- 权限问题

解决方案:
1. 重新配置正确的Cursor路径
2. 检查文件是否存在
3. 手动启动Cursor验证路径
```

## 🔄 版本对比

| 功能 | v1.0 | v2.0 |
|------|------|------|
| Token输入 | ✅ | ✅ |
| 邮箱输入 | ✅ | ❌ (已移除) |
| 路径配置 | ❌ | ✅ (新增) |
| 自动检测 | ✅ | ✅ (增强) |
| 手动选择 | ❌ | ✅ (新增) |
| 容错处理 | 基础 | 增强 |

## 📦 文件说明

| 文件 | 说明 | 推荐用户 |
|------|------|----------|
| `cursor_token_pusher_gui_modern.py` | 现代化美观界面 | 普通用户 |
| `cursor_token_pusher_gui.py` | 基础功能界面 | 技术用户 |
| `push_token.py` | 命令行版本 | 高级用户 |
| `cursor_config.py` | 配置文件 | 自定义配置 |

## 🎉 总结

v2.0版本通过以下改进大大提升了用户体验：

1. **简化操作** - 只需要Token，无需邮箱
2. **增强兼容性** - 支持各种Cursor安装方式
3. **智能检测** - 自动找到Cursor路径
4. **手动配置** - 支持用户自定义路径
5. **更好容错** - 详细的错误提示和解决建议

现在用户只需要提供Token和Cursor路径（通常可以自动检测），就能轻松完成Token推送！
