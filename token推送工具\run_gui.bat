@echo off
chcp 65001 >nul
title Cursor Token Pusher GUI

echo ========================================
echo    Cursor Token Pusher - 图形界面版
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "cursor_token_pusher_gui.py" (
    echo ❌ 错误: 未找到 cursor_token_pusher_gui.py
    echo.
    pause
    exit /b 1
)

if not exist "cursor_token_pusher.py" (
    echo ❌ 错误: 未找到 cursor_token_pusher.py
    echo.
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo 🚀 启动图形界面...
echo.

REM 运行GUI程序
python cursor_token_pusher_gui.py

REM 如果出错，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 程序执行出错
    echo.
    pause
)
