#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cursor检测功能
"""

from cursor_token_pusher import CursorTokenPusher

def test_cursor_detection():
    """测试Cursor检测功能"""
    print("🔍 测试Cursor检测功能...")
    print()
    
    pusher = CursorTokenPusher()
    
    # 测试数据库路径检测
    print(f"📍 Cursor数据库路径: {pusher.db_path}")
    print(f"   存在: {'✅' if pusher.check_cursor_installation() else '❌'}")
    print()
    
    # 测试可执行文件检测
    print(f"🚀 Cursor可执行文件: {pusher.cursor_exe_path}")
    print(f"   存在: {'✅' if pusher.cursor_exe_path else '❌'}")
    print()
    
    if pusher.cursor_exe_path:
        print("✅ 所有检测通过，工具可以正常使用!")
        print("💡 推送Token后将自动启动Cursor")
    else:
        print("⚠️  未找到Cursor可执行文件")
        print("💡 推送Token后需要手动启动Cursor")

if __name__ == "__main__":
    test_cursor_detection()
