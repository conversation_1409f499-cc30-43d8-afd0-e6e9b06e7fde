#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "========================================"
echo "🚀 Cursor Token Pusher 跨平台打包工具"
echo "========================================"
echo

# 检测当前平台
OS=$(uname -s)
case "$OS" in
    Darwin)
        PLATFORM="macOS"
        PYTHON_CMD="python3"
        ;;
    Linux)
        PLATFORM="Linux"
        PYTHON_CMD="python3"
        ;;
    *)
        echo -e "${RED}❌ 不支持的操作系统: $OS${NC}"
        echo "请在Windows、macOS或Linux上运行此脚本"
        exit 1
        ;;
esac

echo -e "${BLUE}📋 当前平台: $PLATFORM${NC}"
echo
echo "📋 支持的平台:"
echo "   - Windows (需要在Windows上运行)"
echo "   - macOS (当前平台)" 
echo "   - Linux (当前平台)"
echo

echo -e "${YELLOW}⚠️  注意: 此脚本只能在$PLATFORM上打包${PLATFORM}版本${NC}"
echo "   要打包其他平台版本，请在对应平台上运行相应脚本"
echo

read -p "按回车键继续..."
echo

# 检查Python环境
echo "🔍 检查Python环境..."
if ! command -v $PYTHON_CMD &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到$PYTHON_CMD，请先安装Python 3.6+${NC}"
    exit 1
fi

PYTHON_VERSION=$($PYTHON_CMD --version 2>&1)
echo -e "${GREEN}✅ Python环境检查通过: $PYTHON_VERSION${NC}"
echo

# 检查必要文件
echo "🔍 检查必要文件..."
REQUIRED_FILES=("cursor_token_pusher_gui_modern.py" "cursor_token_pusher.py" "cursor_config.py" "build_cross_platform.py")

for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo -e "${RED}❌ 错误: 缺少必要文件 $file${NC}"
        exit 1
    fi
done

echo -e "${GREEN}✅ 必要文件检查通过${NC}"
echo

# 开始打包
echo -e "${BLUE}📦 开始${PLATFORM}平台打包...${NC}"
$PYTHON_CMD build_cross_platform.py

if [[ $? -ne 0 ]]; then
    echo -e "${RED}❌ 打包失败${NC}"
    exit 1
fi

echo
echo -e "${GREEN}🎉 ${PLATFORM}平台打包完成！${NC}"
echo

# 显示输出文件
echo "📁 输出文件:"
ls -la CursorTokenPusher_v2.0_${PLATFORM}.* 2>/dev/null || echo "未找到输出文件"
echo

# 显示其他平台打包说明
echo -e "${BLUE}💡 其他平台打包说明:${NC}"
echo

if [[ "$PLATFORM" != "Windows" ]]; then
    echo "🪟 Windows平台:"
    echo "   1. 将项目文件复制到Windows电脑"
    echo "   2. 在命令提示符中运行: python build_cross_platform.py"
    echo "   3. 或双击运行: build_all_platforms.bat"
    echo "   4. 输出: CursorTokenPusher_v2.0_Windows.zip"
    echo
fi

if [[ "$PLATFORM" != "macOS" ]]; then
    echo "🍎 macOS平台:"
    echo "   1. 将项目文件复制到Mac电脑"
    echo "   2. 在终端中运行: python3 build_cross_platform.py"
    echo "   3. 或运行: ./build_all_platforms.sh"
    echo "   4. 输出: CursorTokenPusher_v2.0_macOS.zip"
    echo
fi

if [[ "$PLATFORM" != "Linux" ]]; then
    echo "🐧 Linux平台:"
    echo "   1. 将项目文件复制到Linux电脑"
    echo "   2. 在终端中运行: python3 build_cross_platform.py"
    echo "   3. 或运行: ./build_all_platforms.sh"
    echo "   4. 输出: CursorTokenPusher_v2.0_Linux.zip"
    echo
fi

echo -e "${GREEN}✨ 打包完成！${NC}"

# 询问是否查看详细使用说明
echo
read -p "是否查看跨平台使用说明？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [[ -f "跨平台使用说明.md" ]]; then
        if command -v less &> /dev/null; then
            less "跨平台使用说明.md"
        elif command -v more &> /dev/null; then
            more "跨平台使用说明.md"
        else
            cat "跨平台使用说明.md"
        fi
    else
        echo -e "${YELLOW}⚠️  未找到跨平台使用说明.md文件${NC}"
    fi
fi

echo
echo -e "${GREEN}感谢使用 Cursor Token Pusher！${NC}"
