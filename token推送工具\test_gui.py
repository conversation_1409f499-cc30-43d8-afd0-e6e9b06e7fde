#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI测试脚本 - 验证GUI组件是否正常工作
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_gui_components():
    """测试GUI组件"""
    print("🔍 测试GUI组件...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("400x300")
        
        # 测试基本组件
        ttk.Label(root, text="✅ Label组件正常").pack(pady=5)
        ttk.Entry(root, width=30).pack(pady=5)
        ttk.Button(root, text="✅ Button组件正常").pack(pady=5)
        
        # 测试滚动文本
        from tkinter import scrolledtext
        text_widget = scrolledtext.ScrolledText(root, height=5, width=40)
        text_widget.pack(pady=5)
        text_widget.insert(tk.END, "✅ ScrolledText组件正常\n")
        
        print("✅ 所有GUI组件测试通过")
        
        # 显示窗口2秒后关闭
        root.after(2000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI组件测试失败: {e}")
        return False

def test_imports():
    """测试必要的导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试核心模块
        from cursor_token_pusher import CursorTokenPusher
        print("✅ cursor_token_pusher模块导入成功")
        
        # 测试配置模块
        try:
            from cursor_config import CUSTOM_CURSOR_PATHS
            print("✅ cursor_config模块导入成功")
        except ImportError:
            print("⚠️  cursor_config模块未找到（可选）")
        
        # 测试GUI模块
        import tkinter as tk
        from tkinter import ttk, messagebox, scrolledtext
        print("✅ tkinter模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_cursor_detection():
    """测试Cursor检测"""
    print("🔍 测试Cursor检测...")
    
    try:
        from cursor_token_pusher import CursorTokenPusher
        pusher = CursorTokenPusher()
        
        print(f"📍 数据库路径: {pusher.db_path}")
        print(f"   存在: {'✅' if os.path.exists(pusher.db_path) else '❌'}")
        
        print(f"🚀 可执行文件: {pusher.cursor_exe_path}")
        print(f"   存在: {'✅' if pusher.cursor_exe_path and os.path.exists(pusher.cursor_exe_path) else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Cursor检测失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 GUI版本测试工具")
    print("=" * 40)
    
    tests = [
        ("模块导入", test_imports),
        ("Cursor检测", test_cursor_detection),
        ("GUI组件", test_gui_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        if test_func():
            passed += 1
        print("-" * 40)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI版本可以正常使用")
        print("\n💡 下一步:")
        print("   1. 运行: python cursor_token_pusher_gui.py")
        print("   2. 或者: python build_gui.py (打包成exe)")
    else:
        print("❌ 部分测试失败，请检查环境配置")

if __name__ == "__main__":
    main()
