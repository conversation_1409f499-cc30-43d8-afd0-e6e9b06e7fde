#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Token Pusher - 现代化美观界面版本
使用自定义样式和现代化设计的用户友好界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import sys
import os

# 导入核心功能
from cursor_token_pusher import CursorTokenPusher


class ModernStyle:
    """现代化样式配置"""
    
    # 颜色主题
    COLORS = {
        'primary': '#2563eb',      # 蓝色主色调
        'primary_dark': '#1d4ed8', # 深蓝色
        'success': '#10b981',      # 绿色
        'warning': '#f59e0b',      # 橙色
        'danger': '#ef4444',       # 红色
        'background': '#f8fafc',   # 浅灰背景
        'surface': '#ffffff',      # 白色表面
        'text': '#1f2937',         # 深灰文字
        'text_light': '#6b7280',   # 浅灰文字
        'border': '#e5e7eb',       # 边框颜色
    }
    
    # 字体配置
    FONTS = {
        'title': ('Segoe UI', 18, 'bold'),
        'heading': ('Segoe UI', 12, 'bold'),
        'body': ('Segoe UI', 10),
        'small': ('Segoe UI', 9),
        'mono': ('Consolas', 9),
    }


class CursorTokenPusherModernGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Cursor Token Pusher")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        self.root.configure(bg=ModernStyle.COLORS['background'])
        
        # 设置图标
        try:
            self.root.iconbitmap("cursor.ico")
        except:
            pass
        
        try:
            self.pusher = CursorTokenPusher()
            print("✅ CursorTokenPusher初始化成功")
        except Exception as e:
            print(f"❌ CursorTokenPusher初始化失败: {e}")
            messagebox.showerror("错误", f"初始化失败: {e}")
            return

        try:
            self.setup_styles()
            print("✅ 样式设置成功")
        except Exception as e:
            print(f"❌ 样式设置失败: {e}")

        try:
            self.setup_ui()
            print("✅ UI设置成功")
        except Exception as e:
            print(f"❌ UI设置失败: {e}")
            import traceback
            traceback.print_exc()
            return

        try:
            self.check_cursor_status()
            print("✅ 状态检查启动成功")
        except Exception as e:
            print(f"❌ 状态检查失败: {e}")
    
    def setup_styles(self):
        """设置现代化样式"""
        self.style = ttk.Style()
        
        # 配置主题
        try:
            self.style.theme_use('clam')
        except:
            pass
        
        # 自定义样式
        self.style.configure('Title.TLabel', 
                           font=ModernStyle.FONTS['title'],
                           foreground=ModernStyle.COLORS['text'],
                           background=ModernStyle.COLORS['background'])
        
        self.style.configure('Heading.TLabel',
                           font=ModernStyle.FONTS['heading'],
                           foreground=ModernStyle.COLORS['text'],
                           background=ModernStyle.COLORS['surface'])
        
        self.style.configure('Body.TLabel',
                           font=ModernStyle.FONTS['body'],
                           foreground=ModernStyle.COLORS['text_light'],
                           background=ModernStyle.COLORS['surface'])
        
        # 按钮样式
        self.style.configure('Primary.TButton',
                           font=ModernStyle.FONTS['body'],
                           foreground='white',
                           background=ModernStyle.COLORS['primary'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(20, 10))
        
        self.style.map('Primary.TButton',
                      background=[('active', ModernStyle.COLORS['primary_dark']),
                                ('pressed', ModernStyle.COLORS['primary_dark'])])
        
        self.style.configure('Success.TButton',
                           font=ModernStyle.FONTS['body'],
                           foreground='white',
                           background=ModernStyle.COLORS['success'],
                           borderwidth=0,
                           focuscolor='none',
                           padding=(15, 8))
        
        self.style.configure('Secondary.TButton',
                           font=ModernStyle.FONTS['body'],
                           foreground=ModernStyle.COLORS['text'],
                           background=ModernStyle.COLORS['surface'],
                           borderwidth=1,
                           focuscolor='none',
                           padding=(15, 8))
        
        # 输入框样式
        self.style.configure('Modern.TEntry',
                           font=ModernStyle.FONTS['body'],
                           fieldbackground=ModernStyle.COLORS['surface'],
                           borderwidth=2,
                           relief='solid',
                           padding=(10, 8))
        
        # 框架样式
        self.style.configure('Card.TFrame',
                           background=ModernStyle.COLORS['surface'],
                           relief='solid',
                           borderwidth=1)
        
        self.style.configure('Modern.TLabelframe',
                           background=ModernStyle.COLORS['surface'],
                           relief='solid',
                           borderwidth=1)
        
        self.style.configure('Modern.TLabelframe.Label',
                           font=ModernStyle.FONTS['heading'],
                           foreground=ModernStyle.COLORS['text'],
                           background=ModernStyle.COLORS['surface'])
    
    def setup_ui(self):
        """设置现代化用户界面"""
        try:
            # 主容器
            main_container = tk.Frame(self.root, bg=ModernStyle.COLORS['background'])
            main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 标题区域
            print("创建标题区域...")
            self.create_header(main_container)

            # 状态卡片
            print("创建状态卡片...")
            self.create_status_card(main_container)

            # Token输入卡片
            print("创建Token输入卡片...")
            self.create_token_card(main_container)

            # Cursor路径配置卡片
            print("创建Cursor路径配置卡片...")
            self.create_cursor_path_card(main_container)

            # 操作按钮区域
            print("创建操作按钮区域...")
            self.create_action_buttons(main_container)

            # 日志区域
            print("创建日志区域...")
            self.create_log_area(main_container)

            # 底部区域
            print("创建底部区域...")
            self.create_footer(main_container)

            print("UI创建完成!")
        except Exception as e:
            print(f"UI创建错误: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = tk.Frame(parent, bg=ModernStyle.COLORS['background'])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 主标题
        title_label = tk.Label(header_frame,
                              text="🚀 Cursor Token Pusher",
                              font=ModernStyle.FONTS['title'],
                              fg=ModernStyle.COLORS['text'],
                              bg=ModernStyle.COLORS['background'])
        title_label.pack()
        
        # 副标题
        subtitle_label = tk.Label(header_frame,
                                 text="Cursor Token推送",
                                 font=ModernStyle.FONTS['body'],
                                 fg=ModernStyle.COLORS['text_light'],
                                 bg=ModernStyle.COLORS['background'])
        subtitle_label.pack(pady=(5, 0))
    
    def create_status_card(self, parent):
        """创建状态卡片"""
        # 卡片容器
        card_frame = tk.Frame(parent, bg=ModernStyle.COLORS['surface'], 
                             relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 卡片内容
        content_frame = tk.Frame(card_frame, bg=ModernStyle.COLORS['surface'])
        content_frame.pack(fill=tk.X, padx=20, pady=15)
        
        # 标题
        title_label = tk.Label(content_frame,
                              text="📊 Cursor 状态",
                              font=ModernStyle.FONTS['heading'],
                              fg=ModernStyle.COLORS['text'],
                              bg=ModernStyle.COLORS['surface'])
        title_label.pack(anchor=tk.W)
        
        # 状态信息
        status_frame = tk.Frame(content_frame, bg=ModernStyle.COLORS['surface'])
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 数据库状态
        db_frame = tk.Frame(status_frame, bg=ModernStyle.COLORS['surface'])
        db_frame.pack(fill=tk.X, pady=(0, 8))
        
        tk.Label(db_frame, text="数据库:",
                font=ModernStyle.FONTS['body'],
                fg=ModernStyle.COLORS['text'],
                bg=ModernStyle.COLORS['surface']).pack(side=tk.LEFT)
        
        self.db_status_label = tk.Label(db_frame, text="检查中...",
                                       font=ModernStyle.FONTS['body'],
                                       fg=ModernStyle.COLORS['text_light'],
                                       bg=ModernStyle.COLORS['surface'])
        self.db_status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 可执行文件状态
        exe_frame = tk.Frame(status_frame, bg=ModernStyle.COLORS['surface'])
        exe_frame.pack(fill=tk.X)
        
        tk.Label(exe_frame, text="可执行文件:",
                font=ModernStyle.FONTS['body'],
                fg=ModernStyle.COLORS['text'],
                bg=ModernStyle.COLORS['surface']).pack(side=tk.LEFT)
        
        self.exe_status_label = tk.Label(exe_frame, text="检查中...",
                                        font=ModernStyle.FONTS['body'],
                                        fg=ModernStyle.COLORS['text_light'],
                                        bg=ModernStyle.COLORS['surface'])
        self.exe_status_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def create_token_card(self, parent):
        """创建Token输入卡片"""
        # 卡片容器
        card_frame = tk.Frame(parent, bg=ModernStyle.COLORS['surface'], 
                             relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 卡片内容
        content_frame = tk.Frame(card_frame, bg=ModernStyle.COLORS['surface'])
        content_frame.pack(fill=tk.X, padx=20, pady=15)
        
        # 标题
        title_label = tk.Label(content_frame,
                              text="🔑 Token 信息",
                              font=ModernStyle.FONTS['heading'],
                              fg=ModernStyle.COLORS['text'],
                              bg=ModernStyle.COLORS['surface'])
        title_label.pack(anchor=tk.W)

        # 重要提示
        warning_label = tk.Label(content_frame,
                                text="⚠️ 推送前请先：1) 退出Cursor账号  2) 完全关闭Cursor",
                                font=ModernStyle.FONTS['small'],
                                fg=ModernStyle.COLORS['warning'],
                                bg=ModernStyle.COLORS['surface'])
        warning_label.pack(anchor=tk.W, pady=(5, 10))
        
        # Token输入
        token_frame = tk.Frame(content_frame, bg=ModernStyle.COLORS['surface'])
        token_frame.pack(fill=tk.X, pady=(15, 0))
        
        tk.Label(token_frame, text="WorkosCursorSessionToken:",
                font=ModernStyle.FONTS['body'],
                fg=ModernStyle.COLORS['text'],
                bg=ModernStyle.COLORS['surface']).pack(anchor=tk.W)
        
        # Token输入框容器
        token_input_frame = tk.Frame(token_frame, bg=ModernStyle.COLORS['surface'])
        token_input_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.token_entry = tk.Entry(token_input_frame,
                                   font=ModernStyle.FONTS['body'],
                                   bg=ModernStyle.COLORS['surface'],
                                   fg=ModernStyle.COLORS['text'],
                                   relief='solid',
                                   bd=2,
                                   show="*")
        self.token_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=8, ipadx=10)
        
        # 显示/隐藏按钮
        self.show_token_var = tk.BooleanVar()
        show_btn = tk.Checkbutton(token_input_frame,
                                 text="👁",
                                 variable=self.show_token_var,
                                 command=self.toggle_token_visibility,
                                 font=ModernStyle.FONTS['body'],
                                 bg=ModernStyle.COLORS['surface'],
                                 fg=ModernStyle.COLORS['text'],
                                 selectcolor=ModernStyle.COLORS['surface'],
                                 relief='flat',
                                 bd=0)
        show_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 移除邮箱输入部分

    def create_cursor_path_card(self, parent):
        """创建Cursor路径配置卡片"""
        # 卡片容器
        card_frame = tk.Frame(parent, bg=ModernStyle.COLORS['surface'],
                             relief='solid', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 15))

        # 卡片内容
        content_frame = tk.Frame(card_frame, bg=ModernStyle.COLORS['surface'])
        content_frame.pack(fill=tk.X, padx=20, pady=15)

        # 标题
        title_label = tk.Label(content_frame,
                              text="📁 Cursor 路径配置",
                              font=ModernStyle.FONTS['heading'],
                              fg=ModernStyle.COLORS['text'],
                              bg=ModernStyle.COLORS['surface'])
        title_label.pack(anchor=tk.W)

        # 说明文字
        desc_label = tk.Label(content_frame,
                             text="请指定Cursor可执行文件的完整路径（例如：D:\\cursor\\Cursor.exe）",
                             font=ModernStyle.FONTS['small'],
                             fg=ModernStyle.COLORS['text_light'],
                             bg=ModernStyle.COLORS['surface'])
        desc_label.pack(anchor=tk.W, pady=(5, 10))

        # 路径输入框容器
        path_input_frame = tk.Frame(content_frame, bg=ModernStyle.COLORS['surface'])
        path_input_frame.pack(fill=tk.X)

        # 路径输入框
        self.cursor_path_entry = tk.Entry(path_input_frame,
                                         font=ModernStyle.FONTS['body'],
                                         bg=ModernStyle.COLORS['surface'],
                                         fg=ModernStyle.COLORS['text'],
                                         relief='solid',
                                         bd=2)
        self.cursor_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=8, ipadx=10)

        # 浏览按钮
        browse_btn = tk.Button(path_input_frame,
                              text="📂 浏览",
                              command=self.browse_cursor_path,
                              font=ModernStyle.FONTS['body'],
                              bg=ModernStyle.COLORS['surface'],
                              fg=ModernStyle.COLORS['text'],
                              relief='solid',
                              bd=1,
                              padx=15,
                              pady=8,
                              cursor='hand2')
        browse_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # 自动检测按钮
        auto_detect_btn = tk.Button(path_input_frame,
                                   text="🔍 自动检测",
                                   command=self.auto_detect_cursor_path,
                                   font=ModernStyle.FONTS['body'],
                                   bg=ModernStyle.COLORS['primary'],
                                   fg='white',
                                   relief='flat',
                                   bd=0,
                                   padx=15,
                                   pady=8,
                                   cursor='hand2')
        auto_detect_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # 预填充检测到的路径
        if self.pusher.cursor_exe_path:
            self.cursor_path_entry.insert(0, self.pusher.cursor_exe_path)

    def create_action_buttons(self, parent):
        """创建操作按钮区域"""
        try:
            button_frame = tk.Frame(parent, bg=ModernStyle.COLORS['background'])
            button_frame.pack(fill=tk.X, pady=(0, 15))

            # 主要按钮 - 简化版本
            self.push_button = tk.Button(button_frame,
                                        text="🚀 推送 Token",
                                        command=self.push_token,
                                        font=("Arial", 12, "bold"),
                                        bg="#2563eb",
                                        fg="white",
                                        padx=30,
                                        pady=12)
            self.push_button.pack(side=tk.LEFT, padx=(0, 10))

            # 次要按钮
            backup_btn = tk.Button(button_frame,
                                  text="💾 备份数据库",
                                  command=self.backup_database,
                                  font=("Arial", 10),
                                  padx=20,
                                  pady=10)
            backup_btn.pack(side=tk.LEFT, padx=(0, 10))

            test_btn = tk.Button(button_frame,
                                text="🔍 测试连接",
                                command=self.test_connection,
                                font=("Arial", 10),
                                padx=20,
                                pady=10)
            test_btn.pack(side=tk.LEFT)

            print("✅ 按钮创建成功")
        except Exception as e:
            print(f"❌ 按钮创建失败: {e}")
            import traceback
            traceback.print_exc()
    
    def create_log_area(self, parent):
        """创建日志区域"""
        # 卡片容器
        card_frame = tk.Frame(parent, bg=ModernStyle.COLORS['surface'], 
                             relief='solid', bd=1)
        card_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 卡片内容
        content_frame = tk.Frame(card_frame, bg=ModernStyle.COLORS['surface'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        
        # 标题
        title_label = tk.Label(content_frame,
                              text="📋 操作日志",
                              font=ModernStyle.FONTS['heading'],
                              fg=ModernStyle.COLORS['text'],
                              bg=ModernStyle.COLORS['surface'])
        title_label.pack(anchor=tk.W)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(content_frame,
                                                 height=8,
                                                 font=ModernStyle.FONTS['mono'],
                                                 bg='#1e1e1e',
                                                 fg='#d4d4d4',
                                                 insertbackground='#d4d4d4',
                                                 relief='solid',
                                                 bd=1,
                                                 wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 添加欢迎消息和重要提示
        self.log("🎉 欢迎使用 Cursor Token Pusher!")
        self.log("⚠️ 重要提示：推送前请先退出Cursor账号并完全关闭Cursor!", "warning")
        self.log("💡 然后输入您的 WorkosCursorSessionToken 开始使用")
    
    def create_footer(self, parent):
        """创建底部区域"""
        footer_frame = tk.Frame(parent, bg=ModernStyle.COLORS['background'])
        footer_frame.pack(fill=tk.X)
        
        # 左侧按钮
        clear_btn = tk.Button(footer_frame,
                             text="🗑 清空日志",
                             command=self.clear_log,
                             font=ModernStyle.FONTS['small'],
                             bg=ModernStyle.COLORS['background'],
                             fg=ModernStyle.COLORS['text_light'],
                             relief='flat',
                             bd=0,
                             cursor='hand2')
        clear_btn.pack(side=tk.LEFT)
        
        # 右侧按钮
        help_btn = tk.Button(footer_frame,
                            text="❓ 配置帮助",
                            command=self.show_help,
                            font=ModernStyle.FONTS['small'],
                            bg=ModernStyle.COLORS['background'],
                            fg=ModernStyle.COLORS['text_light'],
                            relief='flat',
                            bd=0,
                            cursor='hand2')
        help_btn.pack(side=tk.RIGHT, padx=(0, 10))

        about_btn = tk.Button(footer_frame,
                             text="ℹ 关于",
                             command=self.show_about,
                             font=ModernStyle.FONTS['small'],
                             bg=ModernStyle.COLORS['background'],
                             fg=ModernStyle.COLORS['text_light'],
                             relief='flat',
                             bd=0,
                             cursor='hand2')
        about_btn.pack(side=tk.RIGHT)
    
    def toggle_token_visibility(self):
        """切换Token显示/隐藏"""
        if self.show_token_var.get():
            self.token_entry.config(show="")
        else:
            self.token_entry.config(show="*")

    def browse_cursor_path(self):
        """浏览选择Cursor可执行文件"""
        if sys.platform == "win32":
            filetypes = [("可执行文件", "*.exe"), ("所有文件", "*.*")]
            title = "选择Cursor.exe文件"
        elif sys.platform == "darwin":
            filetypes = [("应用程序", "*.app"), ("所有文件", "*.*")]
            title = "选择Cursor应用程序"
        else:
            filetypes = [("所有文件", "*.*")]
            title = "选择Cursor可执行文件"

        filename = filedialog.askopenfilename(
            title=title,
            filetypes=filetypes
        )

        if filename:
            self.cursor_path_entry.delete(0, tk.END)
            self.cursor_path_entry.insert(0, filename)
            self.log(f"📁 已选择Cursor路径: {filename}")

    def auto_detect_cursor_path(self):
        """自动检测Cursor路径"""
        def detect():
            self.log("🔍 正在自动检测Cursor路径...")

            # 重新创建pusher实例以重新检测
            from cursor_token_pusher import CursorTokenPusher
            temp_pusher = CursorTokenPusher()

            if temp_pusher.cursor_exe_path:
                self.cursor_path_entry.delete(0, tk.END)
                self.cursor_path_entry.insert(0, temp_pusher.cursor_exe_path)
                self.log(f"✅ 自动检测成功: {temp_pusher.cursor_exe_path}", "success")

                # 更新当前pusher的路径
                self.pusher.cursor_exe_path = temp_pusher.cursor_exe_path
            else:
                self.log("❌ 自动检测失败，请手动选择Cursor路径", "warning")
                messagebox.showwarning("检测失败", "未能自动检测到Cursor路径，请手动选择")

        threading.Thread(target=detect, daemon=True).start()
    
    def log(self, message, level="info"):
        """添加带颜色的日志消息"""
        colors = {
            "info": "#d4d4d4",
            "success": "#4ade80", 
            "warning": "#fbbf24",
            "error": "#f87171"
        }
        
        # 插入消息
        self.log_text.insert(tk.END, f"{message}\n")
        
        # 应用颜色（如果支持）
        try:
            start_line = self.log_text.index(tk.END + "-2l linestart")
            end_line = self.log_text.index(tk.END + "-1l lineend")
            self.log_text.tag_add(level, start_line, end_line)
            self.log_text.tag_config(level, foreground=colors.get(level, colors["info"]))
        except:
            pass
        
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("📋 日志已清空")
    
    def check_cursor_status(self):
        """检查Cursor状态"""
        def check():
            # 检查数据库
            if self.pusher.check_cursor_installation():
                self.db_status_label.config(text="✅ 已找到", fg=ModernStyle.COLORS['success'])
                self.log("✅ Cursor数据库检测成功", "success")
            else:
                self.db_status_label.config(text="❌ 未找到", fg=ModernStyle.COLORS['danger'])
                self.log("❌ Cursor数据库未找到", "error")
            
            # 检查可执行文件
            if self.pusher.cursor_exe_path:
                self.exe_status_label.config(text=f"✅ 已找到", fg=ModernStyle.COLORS['success'])
                self.log(f"✅ Cursor可执行文件: {self.pusher.cursor_exe_path}", "success")
            else:
                self.exe_status_label.config(text="❌ 未找到", fg=ModernStyle.COLORS['danger'])
                self.log("❌ Cursor可执行文件未找到", "error")
        
        threading.Thread(target=check, daemon=True).start()
    
    def backup_database(self):
        """备份数据库"""
        def backup():
            self.log("🔄 正在备份数据库...")
            if self.pusher.backup_database():
                self.log("✅ 数据库备份成功", "success")
                messagebox.showinfo("成功", "数据库备份成功！")
            else:
                self.log("❌ 数据库备份失败", "error")
                messagebox.showerror("错误", "数据库备份失败！")
        
        threading.Thread(target=backup, daemon=True).start()
    
    def test_connection(self):
        """测试连接"""
        def test():
            self.log("🔍 正在测试Cursor连接...")
            if self.pusher.verify_token_installation():
                self.log("✅ Cursor连接正常", "success")
                messagebox.showinfo("测试结果", "Cursor连接正常！")
            else:
                self.log("⚠️ Cursor连接异常或未配置Token", "warning")
                messagebox.showwarning("测试结果", "Cursor连接异常或未配置Token")
        
        threading.Thread(target=test, daemon=True).start()
    
    def push_token(self):
        """推送Token"""
        token = self.token_entry.get().strip()
        cursor_path = self.cursor_path_entry.get().strip()
        email = None  # 不再使用邮箱

        if not token:
            self.log("❌ 请输入WorkosCursorSessionToken", "error")
            messagebox.showerror("错误", "请输入WorkosCursorSessionToken！")
            return

        # 确认用户已经退出账号并关闭Cursor
        confirm_msg = """
⚠️ 推送前确认检查：

1. 是否已经退出Cursor中的当前账号？
2. 是否已经完全关闭Cursor程序？

如果没有完成以上步骤，推送可能会失败或不生效。

确认继续推送吗？
"""
        if not messagebox.askyesno("推送确认", confirm_msg):
            self.log("❌ 用户取消推送操作")
            return

        # 验证Cursor路径
        if cursor_path:
            if not os.path.exists(cursor_path):
                self.log("❌ 指定的Cursor路径不存在", "error")
                messagebox.showerror("错误", f"指定的Cursor路径不存在：\n{cursor_path}")
                return
            # 更新pusher的Cursor路径
            self.pusher.cursor_exe_path = cursor_path
            self.log(f"📁 使用指定的Cursor路径: {cursor_path}")
        elif not self.pusher.cursor_exe_path:
            self.log("⚠️ 未指定Cursor路径，推送后需要手动启动Cursor", "warning")
        
        # 禁用按钮防止重复点击
        self.push_button.config(state="disabled", text="推送中...")
        
        def push():
            try:
                self.log("🚀 开始推送Token...")
                self.log(f"🔑 Token: {token[:20]}...")
                
                # 推送Token
                if self.pusher.push_token(token, email):
                    self.log("✅ Token推送成功！", "success")
                    
                    # 验证安装
                    if self.pusher.verify_token_installation():
                        self.log("✅ Token验证通过", "success")
                        
                        # 询问是否启动Cursor
                        result = messagebox.askyesno("推送成功", 
                                                   "Token推送成功！\n\n是否立即启动Cursor？")
                        if result:
                            self.log("🚀 正在启动Cursor...")
                            if self.pusher.launch_cursor():
                                self.log("✅ Cursor启动成功！", "success")
                                messagebox.showinfo("完成", "Cursor已启动，新的认证信息应该已生效！")
                            else:
                                self.log("❌ Cursor启动失败，请手动启动", "error")
                                messagebox.showwarning("提示", "Cursor启动失败，请手动启动Cursor")
                        else:
                            messagebox.showinfo("完成", "Token推送完成！请重启Cursor以使更改生效。")
                    else:
                        self.log("⚠️ Token验证失败", "warning")
                        messagebox.showwarning("警告", "Token推送可能不完整，请检查！")
                else:
                    self.log("❌ Token推送失败", "error")
                    messagebox.showerror("错误", "Token推送失败！")
                    
            except Exception as e:
                self.log(f"❌ 发生错误: {str(e)}", "error")
                messagebox.showerror("错误", f"发生错误：{str(e)}")
            finally:
                # 重新启用按钮
                self.push_button.config(state="normal", text="🚀 推送 Token")
        
        threading.Thread(target=push, daemon=True).start()
    
    def show_help(self):
        """显示配置帮助"""
        help_text = """
❓ Cursor Token Pusher - 配置帮助

🎯 主要配置项：
• Token: 必需，输入你的WorkosCursorSessionToken
• Cursor路径: 可选，通常可以自动检测

📁 Cursor路径配置：
• 点击"🔍 自动检测" - 自动查找Cursor安装位置
• 点击"📂 浏览" - 手动选择Cursor.exe文件
• 直接输入完整路径

🔍 Windows常见路径：
• D:\\cursor\\Cursor.exe
• C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe
• C:\\Program Files\\Cursor\\Cursor.exe

⚠️ 常见问题：
• 如果自动检测失败，请手动选择Cursor路径
• 推送前请确保Cursor完全关闭
• 如遇权限问题，请以管理员身份运行

💡 提示：
• 数据库路径通常无需配置，程序自动处理
• 工具会自动备份原数据库文件
• 详细帮助请查看"环境配置帮助.md"文件
"""
        messagebox.showinfo("配置帮助", help_text)

    def show_about(self):
        """显示关于信息"""
        about_text = """
🚀 Cursor Token Pusher - 现代化版本

✨ 功能特性:
• 现代化美观界面设计
• 直接推送WorkosCursorSessionToken到Cursor
• 智能Cursor路径检测和配置
• 自动备份数据库
• 自动启动Cursor
• 实时状态监控
• 彩色日志输出

🎯 使用方法:
1. 输入有效的WorkosCursorSessionToken
2. 配置Cursor可执行文件路径（可自动检测或手动选择）
3. 点击"推送Token"按钮
4. 选择是否自动启动Cursor

⚠️ 注意事项:
• 推送前请确保Cursor已完全关闭
• 工具会自动备份原数据库文件
• 如遇问题可使用备份文件恢复

📦 版本: 2.0 (现代化版本)
🎨 界面: 现代化设计风格

👨‍💻 作者: kkk
📧 联系邮箱: <EMAIL>
🔗 项目地址: https://github.com/chengazhen/cursor-auto-free
"""
        messagebox.showinfo("关于 Cursor Token Pusher", about_text)


def main():
    """主函数"""
    root = tk.Tk()
    
    # 设置窗口属性
    root.configure(bg=ModernStyle.COLORS['background'])
    
    app = CursorTokenPusherModernGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        root.quit()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 启动GUI
    root.mainloop()


if __name__ == "__main__":
    main()
