#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版打包脚本 - 只打包核心功能
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def build_simple():
    """简单打包"""
    print("🔨 开始简单打包...")
    
    # 基本的PyInstaller命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 不显示控制台
        "--name=CursorTokenPusher",     # 输出文件名
        "--add-data=cursor_config.py;.", # 添加配置文件
        "cursor_token_pusher_gui_modern.py"  # 主程序
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包成功!")
            
            # 检查输出文件
            exe_path = Path("dist/CursorTokenPusher.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📦 输出文件: {exe_path}")
                print(f"📏 文件大小: {size_mb:.1f} MB")
                return True
            else:
                print("❌ 未找到输出文件")
                return False
        else:
            print(f"❌ 打包失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {str(e)}")
        return False

def create_release():
    """创建发布包"""
    print("📦 创建发布包...")
    
    # 创建发布目录
    release_dir = Path("CursorTokenPusher_Release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制exe文件
    exe_source = Path("dist/CursorTokenPusher.exe")
    if exe_source.exists():
        shutil.copy2(exe_source, release_dir / "CursorTokenPusher.exe")
    else:
        print("❌ 未找到exe文件")
        return False
    
    # 创建使用说明
    readme_content = """
# Cursor Token Pusher v2.0

## 🚀 使用方法
1. 双击运行 CursorTokenPusher.exe
2. 输入你的 WorkosCursorSessionToken
3. 配置Cursor路径（通常自动检测）
4. 点击"推送Token"按钮
5. 完成！

## ⚠️ 重要提示
推送前请务必：
1. 退出Cursor中的当前账号
2. 完全关闭Cursor程序

## 👨‍💻 作者信息
- 作者: kkk
- 邮箱: <EMAIL>
- 版本: v2.0

## 🔧 系统要求
- Windows 7 或更高版本
- 已安装Cursor编辑器

如遇问题请联系: <EMAIL>
"""
    
    with open(release_dir / "使用说明.txt", "w", encoding="utf-8") as f:
        f.write(readme_content.strip())
    
    print(f"✅ 发布包已创建: {release_dir}")
    return True

def cleanup():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    
    temp_dirs = ["build", "dist", "__pycache__"]
    temp_files = ["*.spec"]
    
    for temp_dir in temp_dirs:
        if Path(temp_dir).exists():
            shutil.rmtree(temp_dir)
    
    import glob
    for pattern in temp_files:
        for file in glob.glob(pattern):
            Path(file).unlink()
    
    print("✅ 清理完成")

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 Cursor Token Pusher - 简化打包工具")
    print("👨‍💻 作者: kkk")
    print("=" * 50)
    print()
    
    # 检查必要文件
    required_files = [
        "cursor_token_pusher_gui_modern.py",
        "cursor_token_pusher.py",
        "cursor_config.py"
    ]
    
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            return
    
    print("✅ 文件检查通过")
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return
    
    # 简单打包
    if not build_simple():
        return
    
    # 创建发布包
    if not create_release():
        return
    
    print()
    print("🎉 打包完成！")
    print()
    print("📦 输出文件:")
    print("   - CursorTokenPusher_Release/CursorTokenPusher.exe")
    print("   - CursorTokenPusher_Release/使用说明.txt")
    print()
    
    # 询问是否清理
    try:
        choice = input("🗑️ 是否清理临时文件? (y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            cleanup()
    except KeyboardInterrupt:
        print("\n")
    
    print("✨ 完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ 打包被用户中断")
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
