@echo off
chcp 65001 >nul
title Cursor Token Pusher - 现代化版本

echo ========================================
echo    Cursor Token Pusher - 现代化美观版
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "cursor_token_pusher_gui_modern.py" (
    echo ❌ 错误: 未找到 cursor_token_pusher_gui_modern.py
    echo.
    pause
    exit /b 1
)

if not exist "cursor_token_pusher.py" (
    echo ❌ 错误: 未找到 cursor_token_pusher.py
    echo.
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo 🎨 启动现代化界面...
echo.

REM 运行现代化GUI程序
python cursor_token_pusher_gui_modern.py

REM 如果出错，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 程序执行出错
    echo.
    pause
)
