#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的GUI测试程序
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_button():
    messagebox.showinfo("测试", "按钮工作正常！")

def main():
    try:
        print("正在启动GUI...")
        
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("400x300")
        
        # 创建标题
        title_label = tk.Label(root, text="GUI测试程序", font=("Arial", 16))
        title_label.pack(pady=20)
        
        # 创建按钮
        test_btn = tk.Button(root, 
                           text="测试按钮", 
                           command=test_button,
                           font=("Arial", 12),
                           bg="blue",
                           fg="white",
                           padx=20,
                           pady=10)
        test_btn.pack(pady=20)
        
        # 创建文本框
        text_label = tk.Label(root, text="如果你能看到这个界面和按钮，说明GUI正常工作")
        text_label.pack(pady=10)
        
        print("GUI界面已创建，正在显示...")
        root.mainloop()
        
    except Exception as e:
        print(f"GUI启动失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
