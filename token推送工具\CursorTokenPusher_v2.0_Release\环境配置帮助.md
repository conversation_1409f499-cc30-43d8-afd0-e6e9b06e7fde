# Cursor Token Pusher - 环境配置帮助

## 📋 快速检查清单

### ✅ 用户需要配置的（重要）
- **Cursor可执行文件路径** - 如果自动检测失败

### 🔍 系统自动处理的（通常无需关心）
- **Cursor数据库路径** - 程序自动检测标准位置
- **系统权限** - 程序自动处理
- **数据库备份** - 程序自动备份

## 🎯 用户主要配置：Cursor路径

### Windows系统
```
常见安装位置：
✅ D:\cursor\Cursor.exe                    # 自定义安装（你的情况）
✅ C:\Users\<USER>\AppData\Local\Programs\cursor\Cursor.exe
✅ C:\Program Files\Cursor\Cursor.exe
✅ C:\Program Files (x86)\Cursor\Cursor.exe

如何找到你的Cursor路径：
1. 右键桌面上的Cursor图标 → "属性" → 查看"目标"
2. 或者在任务管理器中找到Cursor进程 → 右键 → "打开文件位置"
3. 或者在开始菜单找到Cursor → 右键 → "更多" → "打开文件位置"
```

### macOS系统
```
标准位置：
✅ /Applications/Cursor.app/Contents/MacOS/Cursor

如何找到：
1. 打开Finder → 应用程序 → 找到Cursor.app
2. 右键Cursor.app → "显示包内容" → Contents → MacOS → Cursor
```

### Linux系统
```
可能的位置：
✅ /usr/bin/cursor
✅ /usr/local/bin/cursor  
✅ /opt/cursor/cursor
✅ ~/.local/bin/cursor

如何找到：
1. 终端运行: which cursor
2. 或者: whereis cursor
3. 或者: find / -name "cursor" 2>/dev/null
```

## 🔧 数据库路径（自动处理，仅供参考）

### Windows
```
标准位置：
%APPDATA%\Cursor\User\globalStorage\state.vscdb

实际路径示例：
C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\state.vscdb

检查方法：
1. Win+R → 输入 %APPDATA%\Cursor\User\globalStorage
2. 查看是否有 state.vscdb 文件
```

### macOS
```
标准位置：
~/Library/Application Support/Cursor/User/globalStorage/state.vscdb

检查方法：
1. Finder → 前往 → 前往文件夹
2. 输入: ~/Library/Application Support/Cursor/User/globalStorage
3. 查看是否有 state.vscdb 文件
```

### Linux
```
标准位置：
~/.config/Cursor/User/globalStorage/state.vscdb

检查方法：
1. 终端运行: ls ~/.config/Cursor/User/globalStorage/state.vscdb
2. 如果文件存在，说明路径正确
```

## 🚨 常见问题和解决方案

### 问题1: "未找到Cursor数据库文件"

**原因：** Cursor从未运行过，或者安装不完整

**解决方案：**
```
1. 启动Cursor一次，让它创建必要的文件
2. 完全关闭Cursor
3. 重新运行Token推送工具
```

### 问题2: "数据库被锁定"

**原因：** Cursor正在运行

**解决方案：**
```
Windows:
1. 任务管理器 → 结束所有Cursor相关进程
2. 或者重启电脑

macOS:
1. 活动监视器 → 强制退出Cursor
2. 或者终端运行: killall Cursor

Linux:
1. 终端运行: pkill cursor
2. 或者: killall cursor
```

### 问题3: "权限不足"

**原因：** 没有足够权限访问Cursor文件

**解决方案：**
```
Windows:
1. 右键程序 → "以管理员身份运行"

macOS:
1. 终端运行: sudo python cursor_token_pusher_gui_modern.py

Linux:
1. 终端运行: sudo python cursor_token_pusher_gui_modern.py
```

### 问题4: "Cursor路径检测失败"

**原因：** Cursor安装在非标准位置

**解决方案：**
```
1. 使用程序中的"📂 浏览"按钮手动选择
2. 或者直接在路径输入框中输入完整路径
3. 确保路径指向可执行文件（.exe文件）
```

## 🔍 环境诊断工具

### 手动检查Cursor安装
```bash
# Windows (PowerShell)
Get-Process | Where-Object {$_.ProcessName -like "*cursor*"}
Get-ChildItem "C:\Program Files" -Recurse -Name "*cursor*" -ErrorAction SilentlyContinue

# macOS/Linux (Terminal)
ps aux | grep -i cursor
find /Applications -name "*Cursor*" 2>/dev/null  # macOS
find /usr -name "*cursor*" 2>/dev/null           # Linux
```

### 检查数据库文件
```bash
# Windows (PowerShell)
Test-Path "$env:APPDATA\Cursor\User\globalStorage\state.vscdb"

# macOS/Linux (Terminal)
ls -la ~/Library/Application\ Support/Cursor/User/globalStorage/state.vscdb  # macOS
ls -la ~/.config/Cursor/User/globalStorage/state.vscdb                       # Linux
```

## 📝 配置文件说明

### cursor_config.py
```python
# 这个文件包含了自定义的Cursor路径配置
# 如果自动检测失败，可以手动编辑这个文件

CUSTOM_CURSOR_PATHS = {
    "win32": [
        "D:\\cursor\\Cursor.exe",  # 你的路径
        # 可以添加更多路径
    ],
    "darwin": [
        "/Applications/Cursor.app/Contents/MacOS/Cursor",
    ],
    "linux": [
        "/usr/bin/cursor",
        "/usr/local/bin/cursor",
    ]
}
```

## 🎯 推荐的使用流程

### 首次使用
```
1. 下载并解压工具包
2. 双击运行 cursor_token_pusher_gui_modern.py
3. 如果Cursor路径自动检测成功 → 直接使用
4. 如果检测失败 → 点击"📂 浏览"手动选择
5. 输入Token并推送
```

### 日常使用
```
1. 确保Cursor已关闭
2. 运行工具
3. 输入新的Token
4. 点击推送
5. 选择是否自动启动Cursor
```

## 💡 高级配置（可选）

### 企业环境配置
```
如果你在企业环境中使用，可能需要：
1. 配置代理设置
2. 添加防病毒软件白名单
3. 申请管理员权限
4. 配置网络访问权限
```

### 多用户环境
```
如果多个用户共用一台电脑：
1. 每个用户都有独立的Cursor数据库
2. 路径会包含用户名
3. 需要在对应用户下运行工具
```

## 🔒 安全注意事项

### Token安全
```
1. 不要在公共场所输入Token
2. 使用完毕后清空剪贴板
3. 定期更换Token
4. 不要截图包含Token的界面
```

### 文件安全
```
1. 工具会自动备份原数据库
2. 备份文件名为: state.vscdb.backup
3. 如有问题可以手动恢复备份
4. 建议定期清理旧备份文件
```

## 📞 获取帮助

### 如果遇到问题
```
1. 查看程序日志输出
2. 检查Cursor是否完全关闭
3. 尝试以管理员身份运行
4. 检查防病毒软件是否拦截
5. 重启电脑后重试
```

### 收集诊断信息
```
如果需要技术支持，请提供：
1. 操作系统版本
2. Cursor安装路径
3. 错误信息截图
4. 程序日志内容
```

## 🎉 总结

**大多数用户只需要：**
1. ✅ 提供Token
2. ✅ 确认或配置Cursor路径（通常自动检测）

**系统自动处理：**
1. ✅ 数据库路径检测
2. ✅ 权限处理
3. ✅ 文件备份
4. ✅ 错误恢复

这个工具设计得尽可能简单，大部分情况下用户只需要关心Token和Cursor路径即可！
