#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本打包脚本
"""

import os
import sys
import subprocess
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

VERSION = "2.0"
AUTHOR = "kkk"
EMAIL = "<EMAIL>"

def main():
    print("🚀 开始打包最终版本...")
    
    # 检查文件
    if not Path("cursor_token_pusher_gui_final.py").exists():
        print("❌ 找不到GUI文件")
        return
    
    if not Path("cursor_token_pusher.py").exists():
        print("❌ 找不到核心文件")
        return
    
    # 安装PyInstaller
    try:
        import PyInstaller
    except ImportError:
        print("📦 安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # 清理旧文件
    for item in ["build", "dist", "__pycache__"]:
        if Path(item).exists():
            shutil.rmtree(item)
    
    # 打包
    print("🔨 开始打包...")
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name=CursorTokenPusher",
        "--add-data=cursor_config.py;.",
        "cursor_token_pusher_gui_final.py"
    ]
    
    result = subprocess.run(cmd)
    if result.returncode != 0:
        print("❌ 打包失败")
        return
    
    # 创建发布包
    print("📦 创建发布包...")
    release_dir = Path(f"CursorTokenPusher_v{VERSION}_Final")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制exe
    exe_file = Path("dist/CursorTokenPusher.exe")
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir / "CursorTokenPusher.exe")
        size_mb = exe_file.stat().st_size / (1024 * 1024)
        print(f"📏 文件大小: {size_mb:.1f} MB")
    else:
        print("❌ 找不到exe文件")
        return
    
    # 复制文档
    docs = [
        ("../使用说明_v2.md", "使用说明_v2.md"),
        ("../环境配置帮助.md", "环境配置帮助.md"),
        ("../用户需要提供的信息.md", "用户需要提供的信息.md")
    ]
    
    for src, dst in docs:
        if Path(src).exists():
            shutil.copy2(src, release_dir / dst)
    
    # 创建README
    readme = f"""
# Cursor Token Pusher v{VERSION} - 最终版

## 🚀 简介
现代化的Cursor Token推送工具，支持一键推送WorkosCursorSessionToken。

## 👨‍💻 作者信息
- 作者: {AUTHOR}
- 邮箱: {EMAIL}
- 版本: v{VERSION}
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🎯 使用方法
1. 双击运行 CursorTokenPusher.exe
2. 输入 WorkosCursorSessionToken
3. 配置Cursor路径（可自动检测）
4. 点击"推送Token"按钮
5. 完成！

## ⚠️ 重要提示
推送前请务必：
1. 退出Cursor中的当前账号
2. 完全关闭Cursor程序

## 🔧 系统要求
- Windows 7 或更高版本
- 已安装Cursor编辑器

## 📞 技术支持
如遇问题请联系: {EMAIL}

---
© 2024 {AUTHOR}. All rights reserved.
"""
    
    with open(release_dir / "README.txt", "w", encoding="utf-8") as f:
        f.write(readme.strip())
    
    # 创建ZIP
    zip_name = f"CursorTokenPusher_v{VERSION}_Final.zip"
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in release_dir.rglob('*'):
            if file_path.is_file():
                zipf.write(file_path, file_path.relative_to(release_dir))
    
    print(f"✅ 打包完成！")
    print(f"📦 输出文件: {zip_name}")
    print(f"📁 发布目录: {release_dir}")
    
    # 清理
    choice = input("\n🗑️ 清理临时文件? (y/N): ").strip().lower()
    if choice in ['y', 'yes']:
        for item in ["build", "dist", "__pycache__"]:
            if Path(item).exists():
                shutil.rmtree(item)
        print("✅ 清理完成")

if __name__ == "__main__":
    main()
