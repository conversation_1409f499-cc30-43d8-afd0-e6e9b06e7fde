#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token配置文件
在这里可以预设token，避免每次都要输入
"""

# 预设的token配置
# 如果不想每次都输入token，可以在这里设置默认token
DEFAULT_TOKEN = ""

# 示例：
# DEFAULT_TOKEN = "your_workos_cursor_session_token_here"

# 多个token配置（可选）
TOKEN_PRESETS = {
    "默认": "",
    # "账号1": "token1_here",
    # "账号2": "token2_here",
}

def get_default_token():
    """获取默认token"""
    return DEFAULT_TOKEN

def get_token_presets():
    """获取token预设"""
    return TOKEN_PRESETS

def save_token(token, name="默认"):
    """保存token到配置文件"""
    # 这里可以实现保存token到文件的逻辑
    # 为了安全考虑，暂时不实现自动保存
    pass
