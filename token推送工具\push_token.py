#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速Token推送工具 - 最简版本
用法: python push_token.py <token> [email]
"""

import sqlite3
import os
import sys
import subprocess


def get_cursor_db_path():
    """获取Cursor数据库路径"""
    if sys.platform == "win32":
        appdata = os.getenv("APPDATA")
        if not appdata:
            raise EnvironmentError("APPDATA环境变量未设置")
        return os.path.join(appdata, "Cursor", "User", "globalStorage", "state.vscdb")
    elif sys.platform == "darwin":
        return os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/state.vscdb")
    elif sys.platform == "linux":
        return os.path.expanduser("~/.config/Cursor/User/globalStorage/state.vscdb")
    else:
        raise NotImplementedError(f"不支持的操作系统: {sys.platform}")


def find_cursor_executable():
    """查找Cursor可执行文件路径"""
    possible_paths = []

    if sys.platform == "win32":  # Windows
        possible_paths = [
            "D:\\cursor\\Cursor.exe",  # 用户提供的路径
            os.path.join(os.getenv("LOCALAPPDATA", ""), "Programs", "cursor", "Cursor.exe"),
            os.path.join(os.getenv("PROGRAMFILES", ""), "Cursor", "Cursor.exe"),
            os.path.join(os.getenv("PROGRAMFILES(X86)", ""), "Cursor", "Cursor.exe"),
        ]
    elif sys.platform == "darwin":  # macOS
        possible_paths = [
            "/Applications/Cursor.app/Contents/MacOS/Cursor",
        ]
    elif sys.platform == "linux":  # Linux
        possible_paths = [
            "/usr/bin/cursor",
            "/usr/local/bin/cursor",
            "/opt/cursor/cursor",
        ]

    for path in possible_paths:
        if os.path.exists(path):
            return path
    return None


def launch_cursor():
    """启动Cursor"""
    cursor_path = find_cursor_executable()
    if not cursor_path:
        print("⚠️  未找到Cursor，请手动启动")
        return False

    try:
        print(f"🚀 启动Cursor: {cursor_path}")
        if sys.platform == "win32":
            subprocess.Popen([cursor_path], creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)
        else:
            subprocess.Popen([cursor_path])
        print("✅ Cursor已启动!")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False


def push_token_to_cursor(token, email=None):
    """推送token到Cursor"""
    db_path = get_cursor_db_path()
    
    if not os.path.exists(db_path):
        print(f"❌ 错误: 未找到Cursor数据库文件: {db_path}")
        print("请确保Cursor已安装并至少运行过一次")
        return False
    
    # 备份数据库
    try:
        import shutil
        backup_path = f"{db_path}.backup"
        shutil.copy2(db_path, backup_path)
        print(f"✅ 已备份数据库到: {backup_path}")
    except Exception as e:
        print(f"⚠️  备份失败: {e}")
    
    # 准备数据
    updates = [
        ("cursorAuth/cachedSignUpType", "Auth_0"),
        ("cursorAuth/accessToken", token),
        ("cursorAuth/refreshToken", token),
    ]
    
    if email:
        updates.append(("cursorAuth/cachedEmail", email))
    
    # 推送到数据库
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        for key, value in updates:
            # 检查是否存在
            cursor.execute("SELECT COUNT(*) FROM itemTable WHERE key = ?", (key,))
            exists = cursor.fetchone()[0] > 0
            
            if exists:
                cursor.execute("UPDATE itemTable SET value = ? WHERE key = ?", (value, key))
            else:
                cursor.execute("INSERT INTO itemTable (key, value) VALUES (?, ?)", (key, value))
            
            print(f"✅ 已设置 {key.split('/')[-1]}")
        
        conn.commit()
        conn.close()
        
        print("🎉 Token推送成功!")

        # 自动启动Cursor
        print()
        if launch_cursor():
            print("💡 Cursor已启动，新认证信息应该已生效")
        else:
            print("💡 请手动启动Cursor以使更改生效")

        return True
        
    except Exception as e:
        print(f"❌ 推送失败: {e}")
        return False


def main():
    if len(sys.argv) < 2:
        print("用法: python push_token.py <WorkosCursorSessionToken>")
        print("示例: python push_token.py your_token_here")
        return

    token = sys.argv[1].strip()
    email = None  # 不再使用邮箱
    
    if not token:
        print("❌ Token不能为空")
        return
    
    print("🚀 开始推送Token到Cursor...")
    print(f"🔑 Token: {token[:20]}...")
    print()
    
    push_token_to_cursor(token, email)


if __name__ == "__main__":
    main()
