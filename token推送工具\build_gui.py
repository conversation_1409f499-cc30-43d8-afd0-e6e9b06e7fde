#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将GUI版本打包成exe
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        print("正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def create_spec_file():
    """创建PyInstaller spec文件"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['cursor_token_pusher_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('cursor_config.py', '.'),
    ],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CursorTokenPusher',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='cursor.ico' if os.path.exists('cursor.ico') else None,
)
'''
    
    with open('cursor_token_pusher.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("✅ 已创建spec文件")

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "cursor_token_pusher.spec"]
        subprocess.check_call(cmd)
        
        print("✅ 构建完成！")
        
        # 检查输出文件
        exe_path = Path("dist/CursorTokenPusher.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📦 输出文件: {exe_path}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
            return True
        else:
            print("❌ 未找到输出文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def create_portable_package():
    """创建便携版包"""
    print("📦 创建便携版包...")
    
    package_dir = Path("CursorTokenPusher_Portable")
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    # 复制exe文件
    exe_source = Path("dist/CursorTokenPusher.exe")
    if exe_source.exists():
        shutil.copy2(exe_source, package_dir / "CursorTokenPusher.exe")
    
    # 复制配置文件
    config_files = [
        "cursor_config.py",
        "TOKEN_PUSHER_README.md",
    ]
    
    for file in config_files:
        if Path(file).exists():
            shutil.copy2(file, package_dir / file)
    
    # 创建使用说明
    readme_content = """
# Cursor Token Pusher - 便携版

## 使用方法

1. 双击 `CursorTokenPusher.exe` 启动程序
2. 在界面中输入你的 WorkosCursorSessionToken
3. 可选输入邮箱地址
4. 点击"推送Token"按钮
5. 选择是否自动启动Cursor

## 注意事项

- 推送前请确保Cursor已完全关闭
- 程序会自动备份原数据库文件
- 如遇问题可查看详细的使用说明文档

## 文件说明

- `CursorTokenPusher.exe` - 主程序
- `cursor_config.py` - 配置文件（可自定义Cursor路径）
- `TOKEN_PUSHER_README.md` - 详细使用说明

## 系统要求

- Windows 7 或更高版本
- 已安装Cursor编辑器

版本: 1.0
"""
    
    with open(package_dir / "使用说明.txt", "w", encoding="utf-8") as f:
        f.write(readme_content.strip())
    
    print(f"✅ 便携版包已创建: {package_dir}")

def main():
    """主函数"""
    print("🚀 Cursor Token Pusher GUI 打包工具")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "cursor_token_pusher_gui.py",
        "cursor_token_pusher.py",
        "cursor_config.py"
    ]
    
    missing_files = [f for f in required_files if not Path(f).exists()]
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return
    
    print("✅ 所有必要文件已找到")
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        return
    
    # 创建spec文件
    create_spec_file()
    
    # 构建exe
    if build_exe():
        # 创建便携版包
        create_portable_package()
        
        print("\n🎉 打包完成！")
        print("\n📁 输出文件:")
        print("   - dist/CursorTokenPusher.exe (单文件版)")
        print("   - CursorTokenPusher_Portable/ (便携版包)")
        print("\n💡 建议使用便携版包分发给用户")
    else:
        print("\n❌ 打包失败")

if __name__ == "__main__":
    main()
