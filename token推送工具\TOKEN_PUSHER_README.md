# Cursor Token Pusher 使用说明

## 简介

这是一个简化的Cursor Token推送工具，允许用户直接输入`WorkosCursorSessionToken`并自动推送到Cursor本地数据库，无需完整的账号注册流程。

## 文件说明

### 1. `cursor_token_pusher.py` - 交互式版本
- 提供友好的用户界面
- 包含完整的错误检查和验证
- 自动备份数据库
- 支持验证推送结果
- **自动启动Cursor功能**

### 2. `push_token.py` - 命令行版本
- 快速简洁的命令行工具
- 适合脚本化使用
- 最小化依赖
- **自动启动Cursor功能**

## 使用方法

### 方法一：交互式使用

```bash
python cursor_token_pusher.py
```

程序会引导你输入：
1. `WorkosCursorSessionToken` (必需)
2. 邮箱地址 (可选)

### 方法二：命令行使用

```bash
# 只推送token
python push_token.py <your_token_here>

# 推送token和邮箱
python push_token.py <your_token_here> <EMAIL>
```

## 需要的信息

### 必需信息
- **WorkosCursorSessionToken**: Cursor的会话令牌

### 可选信息
- **邮箱地址**: 关联的邮箱账号（建议提供）

## 如何获取WorkosCursorSessionToken

### 方法1：从浏览器Cookie获取
1. 在浏览器中登录 `https://authenticator.cursor.sh`
2. 打开开发者工具 (F12)
3. 转到 Application/Storage → Cookies
4. 查找名为 `WorkosCursorSessionToken` 的cookie
5. 复制其值，去掉 `%3A%3A` 后面的部分

### 方法2：从现有Cursor安装获取
如果你已经有一个正常工作的Cursor安装，可以从数据库中提取token：

```python
import sqlite3
import os

# Windows路径
db_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "state.vscdb")

conn = sqlite3.connect(db_path)
cursor = conn.cursor()
cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/accessToken'")
result = cursor.fetchone()
if result:
    print(f"Token: {result[0]}")
conn.close()
```

## 推送到Cursor的数据

工具会在Cursor数据库中设置以下字段：

| 字段 | 值 | 说明 |
|------|-----|------|
| `cursorAuth/cachedSignUpType` | `Auth_0` | 登录类型标识 |
| `cursorAuth/accessToken` | 用户提供的token | 访问令牌 |
| `cursorAuth/refreshToken` | 用户提供的token | 刷新令牌 |
| `cursorAuth/cachedEmail` | 用户提供的邮箱 | 缓存的邮箱地址 |

## 新功能：自动启动Cursor

推送完成后，工具会：
1. **交互式版本**: 询问用户是否立即启动Cursor
2. **命令行版本**: 自动尝试启动Cursor

### 支持的Cursor安装路径

工具会自动检测以下路径：

**Windows:**
- `D:\cursor\Cursor.exe` (你的安装路径)
- `%LOCALAPPDATA%\Programs\cursor\Cursor.exe`
- `%PROGRAMFILES%\Cursor\Cursor.exe`
- `%PROGRAMFILES(X86)%\Cursor\Cursor.exe`

**macOS:**
- `/Applications/Cursor.app/Contents/MacOS/Cursor`

**Linux:**
- `/usr/bin/cursor`
- `/usr/local/bin/cursor`
- `/opt/cursor/cursor`

## 支持的操作系统

- ✅ Windows
- ✅ macOS
- ✅ Linux

## 数据库路径

工具会自动检测Cursor数据库位置：

- **Windows**: `%APPDATA%\Cursor\User\globalStorage\state.vscdb`
- **macOS**: `~/Library/Application Support/Cursor/User/globalStorage/state.vscdb`
- **Linux**: `~/.config/Cursor/User/globalStorage/state.vscdb`

## 安全特性

1. **自动备份**: 修改前自动备份原数据库文件
2. **验证检查**: 推送后验证数据是否正确写入
3. **错误处理**: 完善的错误处理和回滚机制

## 注意事项

1. **关闭Cursor**: 推送前请确保Cursor完全关闭
2. **自动启动**: 推送完成后工具会自动启动Cursor，无需手动重启
3. **备份恢复**: 如果出现问题，可以使用 `.backup` 文件恢复
4. **Token有效性**: 确保提供的token是有效的且未过期
5. **路径检测**: 如果工具无法找到Cursor，请确保安装路径正确

## 故障排除

### 问题1：找不到数据库文件
```
❌ 错误: 未找到Cursor数据库文件
```
**解决方案**: 确保Cursor已安装并至少运行过一次

### 问题2：权限错误
```
❌ 数据库错误: database is locked
```
**解决方案**: 完全关闭Cursor后重试

### 问题3：Token无效
推送成功但Cursor仍显示未登录
**解决方案**: 检查token是否正确，是否已过期

## 示例

### 完整示例
```bash
# 使用交互式工具
python cursor_token_pusher.py

# 使用命令行工具
python push_token.py eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9... <EMAIL>
```

### 验证推送结果
推送完成后，可以检查Cursor数据库：

```python
import sqlite3
import os

db_path = os.path.join(os.getenv("APPDATA"), "Cursor", "User", "globalStorage", "state.vscdb")
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 检查所有认证相关字段
auth_fields = [
    "cursorAuth/accessToken",
    "cursorAuth/refreshToken", 
    "cursorAuth/cachedEmail",
    "cursorAuth/cachedSignUpType"
]

for field in auth_fields:
    cursor.execute("SELECT value FROM itemTable WHERE key = ?", (field,))
    result = cursor.fetchone()
    print(f"{field}: {result[0] if result else 'Not found'}")

conn.close()
```

## 许可证

本工具基于原项目的许可证，仅供学习和个人使用。
