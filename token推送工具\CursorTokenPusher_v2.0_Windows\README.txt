# Cursor Token Pusher v2.0 (Windows)

## 🚀 简介
现代化的Cursor Token推送工具，支持一键推送WorkosCursorSessionToken到Cursor编辑器。

## 👨‍💻 作者信息
- **作者**: kkk
- **邮箱**: <EMAIL>
- **版本**: v2.0
- **平台**: Windows
- **构建时间**: 2025-07-21 11:50:16

## 🎯 使用方法
1. 双击运行 `CursorTokenPusher.exe`
2. 输入你的 WorkosCursorSessionToken
3. 配置Cursor路径（通常自动检测）
4. 点击"推送Token"按钮
5. 完成！

## ⚠️ 重要提示
**推送前请务必：**
1. 退出Cursor中的当前账号
2. 完全关闭Cursor程序

## 📖 详细说明
请查看以下文档：
- `使用说明_v2.md` - 详细使用指南
- `环境配置帮助.md` - 环境配置说明
- `用户需要提供的信息.md` - 快速入门指南

## 🔧 系统要求
- Windows 7 或更高版本
- 已安装Cursor编辑器

## 📞 技术支持
如遇问题请联系: <EMAIL>

---
© 2024 kkk. All rights reserved.