# Cursor Token Pusher - 跨平台使用说明

## 🌍 支持的平台

本工具支持以下操作系统：
- **Windows** 7/8/10/11 (x64)
- **macOS** 10.12+ (Intel & Apple Silicon)
- **Linux** (主要发行版)

## 📦 打包和分发

### 在Windows上打包

1. **环境准备**
   ```bash
   # 安装Python 3.6+
   # 安装依赖
   pip install pyinstaller
   ```

2. **执行打包**
   ```bash
   cd token推送工具
   python build_cross_platform.py
   ```

3. **输出文件**
   - `CursorTokenPusher_v2.0_Windows.zip` - Windows发布包
   - `CursorTokenPusher_v2.0_Windows/` - 解压后的文件夹

### 在macOS上打包

1. **环境准备**
   ```bash
   # 安装Python 3.6+
   # 安装依赖
   pip3 install pyinstaller
   ```

2. **执行打包**
   ```bash
   cd token推送工具
   python3 build_cross_platform.py
   ```

3. **输出文件**
   - `CursorTokenPusher_v2.0_macOS.zip` - macOS发布包
   - `CursorTokenPusher_v2.0_macOS/` - 解压后的文件夹

### 在Linux上打包

1. **环境准备**
   ```bash
   # 安装Python 3.6+
   sudo apt-get install python3-pip  # Ubuntu/Debian
   # 或
   sudo yum install python3-pip      # CentOS/RHEL
   
   # 安装依赖
   pip3 install pyinstaller
   ```

2. **执行打包**
   ```bash
   cd token推送工具
   python3 build_cross_platform.py
   ```

3. **输出文件**
   - `CursorTokenPusher_v2.0_Linux.zip` - Linux发布包
   - `CursorTokenPusher_v2.0_Linux/` - 解压后的文件夹

## 🚀 使用方法

### Windows用户

1. **下载和解压**
   - 下载 `CursorTokenPusher_v2.0_Windows.zip`
   - 解压到任意文件夹

2. **运行程序**
   - 双击 `CursorTokenPusher.exe`
   - 或在命令行中运行：`CursorTokenPusher.exe`

3. **Cursor路径配置**
   - 程序会自动检测常见安装位置：
     - `%LOCALAPPDATA%\Programs\cursor\Cursor.exe`
     - `%PROGRAMFILES%\Cursor\Cursor.exe`
     - `D:\cursor\Cursor.exe`
   - 如果自动检测失败，可以手动选择路径

### macOS用户

1. **下载和解压**
   - 下载 `CursorTokenPusher_v2.0_macOS.zip`
   - 解压到任意文件夹

2. **运行程序**
   - 双击 `CursorTokenPusher.app`
   - 或在终端中运行：`./CursorTokenPusher`

3. **权限设置**
   - 首次运行可能需要在"系统偏好设置 > 安全性与隐私"中允许运行
   - 或在终端中执行：`chmod +x CursorTokenPusher`

4. **Cursor路径配置**
   - 程序会自动检测：
     - `/Applications/Cursor.app/Contents/MacOS/Cursor`
     - `~/Applications/Cursor.app/Contents/MacOS/Cursor`

### Linux用户

1. **下载和解压**
   - 下载 `CursorTokenPusher_v2.0_Linux.zip`
   - 解压到任意文件夹

2. **设置执行权限**
   ```bash
   chmod +x CursorTokenPusher
   ```

3. **运行程序**
   ```bash
   ./CursorTokenPusher
   ```

4. **Cursor路径配置**
   - 程序会自动检测：
     - `/usr/bin/cursor`
     - `/usr/local/bin/cursor`
     - `~/.local/bin/cursor`
     - `/opt/cursor/cursor`

## 🔧 平台特定配置

### Windows特殊说明

- **管理员权限**：某些情况下可能需要以管理员身份运行
- **防病毒软件**：可能需要将程序添加到白名单
- **路径格式**：使用反斜杠 `\` 或正斜杠 `/` 都可以

### macOS特殊说明

- **Gatekeeper**：首次运行需要在安全设置中允许
- **应用程序包**：Cursor通常安装为 `.app` 包
- **权限**：可能需要授予访问文件系统的权限

### Linux特殊说明

- **发行版差异**：不同发行版的Cursor安装位置可能不同
- **依赖库**：确保系统有必要的GUI库（通常已预装）
- **AppImage**：如果Cursor是AppImage格式，路径会有所不同

## 📁 数据库路径

各平台的Cursor数据库路径：

### Windows
```
%APPDATA%\Cursor\User\globalStorage\state.vscdb
```
实际路径示例：
```
C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\state.vscdb
```

### macOS
```
~/Library/Application Support/Cursor/User/globalStorage/state.vscdb
```
实际路径示例：
```
/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/state.vscdb
```

### Linux
```
~/.config/Cursor/User/globalStorage/state.vscdb
```
实际路径示例：
```
/home/<USER>/.config/Cursor/User/globalStorage/state.vscdb
```

## ⚠️ 重要提示

### 通用注意事项

1. **推送前准备**
   - 退出Cursor中的当前账号
   - 完全关闭Cursor程序
   - 确保有有效的WorkosCursorSessionToken

2. **数据安全**
   - 程序会自动备份原数据库文件
   - 备份文件名为 `state.vscdb.backup`
   - 如遇问题可用备份文件恢复

3. **网络要求**
   - 推送过程不需要网络连接
   - 但Cursor验证token时需要网络

### 故障排除

1. **程序无法启动**
   - 检查是否有执行权限
   - 确认系统兼容性
   - 查看错误日志

2. **找不到Cursor**
   - 手动指定Cursor路径
   - 检查Cursor是否正确安装
   - 确认路径格式正确

3. **Token推送失败**
   - 确认Cursor已完全关闭
   - 检查数据库文件权限
   - 验证Token格式正确

## 📞 技术支持

- **作者**：kkk
- **邮箱**：<EMAIL>
- **项目地址**：https://github.com/chengazhen/cursor-auto-free

如遇问题请提供：
1. 操作系统版本
2. Cursor版本
3. 错误信息截图
4. 详细操作步骤

---

© 2024 kkk. All rights reserved.
