# Cursor Token Pusher - 图形界面版

## 项目概述

这是一个用户友好的图形界面工具，用于将 `WorkosCursorSessionToken` 直接推送到Cursor编辑器，无需复杂的账号注册流程。

## 版本对比

| 版本 | 文件 | 特点 | 适用用户 |
|------|------|------|----------|
| 命令行版 | `cursor_token_pusher.py` | 轻量、快速 | 技术用户 |
| 图形界面版 | `cursor_token_pusher_gui.py` | 友好、直观 | 普通用户 |
| 便携exe版 | `CursorTokenPusher.exe` | 免安装、分发 | 所有用户 |

## 功能特性

### 🎯 核心功能
- ✅ 图形化Token输入界面
- ✅ 实时Cursor状态检测
- ✅ 自动数据库备份
- ✅ 一键推送Token
- ✅ 自动启动Cursor
- ✅ 操作日志显示

### 🛡️ 安全特性
- ✅ Token输入隐藏显示
- ✅ 自动备份原数据库
- ✅ 错误处理和回滚
- ✅ 操作状态实时反馈

### 🔧 用户体验
- ✅ 直观的图形界面
- ✅ 实时状态显示
- ✅ 详细的操作日志
- ✅ 友好的错误提示

## 使用方法

### 方法1：直接运行Python脚本

```bash
# 安装依赖（如果需要）
pip install tkinter  # 通常Python自带

# 运行GUI版本
python cursor_token_pusher_gui.py
```

### 方法2：使用批处理文件（Windows）

双击 `run_gui.bat` 文件

### 方法3：使用打包的exe文件

1. 运行打包脚本：
```bash
python build_gui.py
```

2. 使用生成的exe文件：
```
dist/CursorTokenPusher.exe
```

## 界面说明

### 主界面布局

```
┌─────────────────────────────────────────┐
│           🚀 Cursor Token Pusher        │
├─────────────────────────────────────────┤
│ Cursor 状态                             │
│ 数据库: ✅ 已找到                       │
│ 可执行文件: ✅ D:\cursor\Cursor.exe     │
├─────────────────────────────────────────┤
│ Token 信息                              │
│ WorkosCursorSessionToken: [***********] │
│ 邮箱 (可选): [<EMAIL>]        │
├─────────────────────────────────────────┤
│ [🚀推送Token] [💾备份数据库] [🔍测试连接] │
├─────────────────────────────────────────┤
│ 操作日志                                │
│ ┌─────────────────────────────────────┐ │
│ │ 🔄 正在推送Token...                 │ │
│ │ ✅ Token推送成功！                  │ │
│ │ 🚀 正在启动Cursor...               │ │
│ │ ✅ Cursor启动成功！                 │ │
│ └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│ [清空日志]                      [关于] │
└─────────────────────────────────────────┘
```

### 操作流程

1. **检查状态** - 程序启动时自动检测Cursor安装状态
2. **输入Token** - 在Token输入框中粘贴你的 `WorkosCursorSessionToken`
3. **输入邮箱** - 可选输入关联的邮箱地址
4. **推送Token** - 点击"推送Token"按钮开始推送
5. **启动Cursor** - 推送成功后选择是否自动启动Cursor

## 打包分发

### 自动打包

运行打包脚本：
```bash
python build_gui.py
```

这将：
1. 自动安装PyInstaller（如果未安装）
2. 创建优化的spec文件
3. 构建单文件exe
4. 创建便携版包

### 手动打包

如果需要自定义打包选项：

```bash
# 安装PyInstaller
pip install pyinstaller

# 基础打包
pyinstaller --onefile --windowed cursor_token_pusher_gui.py

# 高级打包（推荐）
pyinstaller --onefile --windowed --name="CursorTokenPusher" --add-data="cursor_config.py;." cursor_token_pusher_gui.py
```

### 打包选项说明

| 选项 | 说明 |
|------|------|
| `--onefile` | 打包成单个exe文件 |
| `--windowed` | 不显示控制台窗口 |
| `--name` | 指定输出文件名 |
| `--add-data` | 包含额外的数据文件 |
| `--icon` | 指定程序图标 |

## 部署建议

### 给普通用户

**推荐：便携版包**
- 包含exe文件和配置文件
- 解压即用，无需安装
- 包含使用说明

### 给技术用户

**推荐：Python脚本**
- 源码透明，可自定义
- 体积小，启动快
- 易于修改和扩展

### 企业内部分发

**推荐：自定义打包**
- 可以预配置Cursor路径
- 可以添加企业Logo
- 可以集成到现有工具链

## 技术架构

### 核心组件

```
cursor_token_pusher_gui.py     # GUI主程序
├── cursor_token_pusher.py     # 核心推送逻辑
├── cursor_config.py           # 配置管理
└── tkinter                    # GUI框架
```

### 依赖关系

- **Python 3.6+** - 基础运行环境
- **tkinter** - GUI框架（Python内置）
- **sqlite3** - 数据库操作（Python内置）
- **threading** - 多线程支持（Python内置）

### 优势分析

**相比Web方案：**
- ✅ 无需浏览器
- ✅ 启动速度快
- ✅ 资源占用少
- ✅ 更像"原生"应用

**相比PyQt方案：**
- ✅ 无额外依赖
- ✅ 打包体积小
- ✅ 学习成本低
- ✅ 维护简单

## 常见问题

### Q: 为什么选择Tkinter而不是其他GUI框架？

A: 
- **轻量级** - Python内置，无需额外安装
- **足够用** - 对于这个简单工具，功能完全够用
- **兼容性好** - 跨平台一致性
- **打包友好** - 打包后体积小

### Q: 如何自定义Cursor安装路径？

A: 修改 `cursor_config.py` 文件中的路径配置

### Q: 打包后的exe文件太大怎么办？

A: 
- 使用 `--exclude-module` 排除不需要的模块
- 使用UPX压缩（`--upx-dir`）
- 考虑使用目录模式而非单文件模式

### Q: 如何添加程序图标？

A: 
1. 准备 `.ico` 格式的图标文件
2. 在打包时使用 `--icon=your_icon.ico` 参数

## 更新日志

- **v1.0** - 初始版本
  - 基础GUI界面
  - Token推送功能
  - 自动启动Cursor
  - 打包脚本

## 许可证

本项目基于原项目许可证，仅供学习和个人使用。
