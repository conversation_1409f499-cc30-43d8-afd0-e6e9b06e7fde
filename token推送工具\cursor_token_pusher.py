#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Token Pusher - 简化版Token推送工具
直接将用户提供的WorkosCursorSessionToken推送到Cursor本地数据库
"""

import sqlite3
import os
import sys
import json
import subprocess
import time
from pathlib import Path
from typing import Optional

# 尝试导入配置文件
try:
    from cursor_config import CUSTOM_CURSOR_PATHS
except ImportError:
    CUSTOM_CURSOR_PATHS = None


class CursorTokenPusher:
    """Cursor Token推送器"""

    def __init__(self):
        self.db_path = self._get_cursor_db_path()
        self.cursor_exe_path = self._find_cursor_executable()
        
    def _get_cursor_db_path(self) -> str:
        """获取Cursor数据库路径"""
        if sys.platform == "win32":  # Windows
            appdata = os.getenv("APPDATA")
            if appdata is None:
                raise EnvironmentError("APPDATA 环境变量未设置")
            db_path = os.path.join(appdata, "Cursor", "User", "globalStorage", "state.vscdb")
        elif sys.platform == "darwin":  # macOS
            db_path = os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/state.vscdb")
        elif sys.platform == "linux":  # Linux
            db_path = os.path.expanduser("~/.config/Cursor/User/globalStorage/state.vscdb")
        else:
            raise NotImplementedError(f"不支持的操作系统: {sys.platform}")
        
        return os.path.abspath(db_path)

    def _process_token(self, token: str) -> str:
        """
        处理Token格式

        Args:
            token: 用户输入的Token

        Returns:
            str: 处理后的Token
        """
        # 如果Token包含%3A%3A，说明是完整的cookie值，需要分割
        if "%3A%3A" in token:
            parts = token.split("%3A%3A")
            if len(parts) >= 2:
                processed = parts[1]
                print(f"🔧 检测到完整cookie格式，提取Token: {processed[:20]}...")
                return processed

        # 否则假设用户已经提供了正确的Token部分
        print(f"🔧 使用提供的Token: {token[:20]}...")
        return token

    def _find_cursor_executable(self) -> Optional[str]:
        """查找Cursor可执行文件路径"""
        possible_paths = []

        # 优先使用配置文件中的路径
        if CUSTOM_CURSOR_PATHS and sys.platform in CUSTOM_CURSOR_PATHS:
            possible_paths.extend(CUSTOM_CURSOR_PATHS[sys.platform])

        # 添加默认路径作为备选
        if sys.platform == "win32":  # Windows
            default_paths = [
                os.path.join(os.getenv("LOCALAPPDATA", ""), "Programs", "cursor", "Cursor.exe"),
                os.path.join(os.getenv("PROGRAMFILES", ""), "Cursor", "Cursor.exe"),
                os.path.join(os.getenv("PROGRAMFILES(X86)", ""), "Cursor", "Cursor.exe"),
                "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe".format(os.getenv("USERNAME", "")),
            ]
            possible_paths.extend(default_paths)
        elif sys.platform == "darwin":  # macOS
            default_paths = [
                "/Applications/Cursor.app/Contents/MacOS/Cursor",
                os.path.expanduser("~/Applications/Cursor.app/Contents/MacOS/Cursor"),
            ]
            possible_paths.extend(default_paths)
        elif sys.platform == "linux":  # Linux
            default_paths = [
                "/usr/bin/cursor",
                "/usr/local/bin/cursor",
                os.path.expanduser("~/.local/bin/cursor"),
                "/opt/cursor/cursor",
            ]
            possible_paths.extend(default_paths)

        # 检查哪个路径存在
        for path in possible_paths:
            if os.path.exists(path):
                return path

        # 如果都不存在，尝试在PATH中查找
        try:
            if sys.platform == "win32":
                result = subprocess.run(["where", "cursor"], capture_output=True, text=True)
            else:
                result = subprocess.run(["which", "cursor"], capture_output=True, text=True)

            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass

        return None

    def check_cursor_installation(self) -> bool:
        """检查Cursor是否已安装"""
        if not os.path.exists(self.db_path):
            print(f"❌ 错误: 未找到Cursor数据库文件")
            print(f"   预期路径: {self.db_path}")
            print(f"   请确保Cursor已正确安装并至少运行过一次")
            return False
        return True
    
    def backup_database(self) -> bool:
        """备份数据库文件"""
        try:
            backup_path = f"{self.db_path}.backup"
            import shutil
            shutil.copy2(self.db_path, backup_path)
            print(f"✅ 数据库已备份到: {backup_path}")
            return True
        except Exception as e:
            print(f"❌ 备份失败: {str(e)}")
            return False
    
    def push_token(self, token: str, email: Optional[str] = None) -> bool:
        """
        推送token到Cursor数据库
        
        Args:
            token: WorkosCursorSessionToken
            email: 邮箱地址（可选）
            
        Returns:
            bool: 是否成功推送
        """
        if not token or not token.strip():
            print("❌ 错误: Token不能为空")
            return False
        
        # 处理Token格式
        processed_token = self._process_token(token.strip())

        # 准备要更新的数据
        updates = [
            ("cursorAuth/cachedSignUpType", "Auth_0"),
            ("cursorAuth/accessToken", processed_token),
            ("cursorAuth/refreshToken", processed_token),
        ]
        
        if email and email.strip():
            updates.append(("cursorAuth/cachedEmail", email.strip()))
        
        # 检查并关闭Cursor进程
        self._ensure_cursor_closed()

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            print("🔄 正在推送Token到Cursor...")
            
            for key, value in updates:
                # 检查键是否存在
                check_query = "SELECT COUNT(*) FROM itemTable WHERE key = ?"
                cursor.execute(check_query, (key,))
                
                if cursor.fetchone()[0] == 0:
                    # 插入新记录
                    insert_query = "INSERT INTO itemTable (key, value) VALUES (?, ?)"
                    cursor.execute(insert_query, (key, value))
                    action = "插入"
                else:
                    # 更新现有记录
                    update_query = "UPDATE itemTable SET value = ? WHERE key = ?"
                    cursor.execute(update_query, (value, key))
                    action = "更新"
                
                if cursor.rowcount > 0:
                    field_name = key.split('/')[-1]
                    print(f"   ✅ 成功{action} {field_name}")
                else:
                    print(f"   ⚠️  {key} 未发生变化")
            
            conn.commit()
            print("✅ Token推送完成!")
            return True
            
        except sqlite3.Error as e:
            print(f"❌ 数据库错误: {str(e)}")
            return False
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")
            return False
        finally:
            if conn:
                conn.close()
    
    def verify_token_installation(self) -> bool:
        """验证Token是否成功安装"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查关键字段
            check_fields = [
                "cursorAuth/accessToken",
                "cursorAuth/refreshToken",
                "cursorAuth/cachedSignUpType"
            ]
            
            print("🔍 验证Token安装状态...")
            all_present = True
            
            for field in check_fields:
                cursor.execute("SELECT value FROM itemTable WHERE key = ?", (field,))
                result = cursor.fetchone()
                
                if result:
                    field_name = field.split('/')[-1]
                    print(f"   ✅ {field_name}: 已设置")
                else:
                    print(f"   ❌ {field}: 未找到")
                    all_present = False
            
            conn.close()
            return all_present
            
        except Exception as e:
            print(f"❌ 验证失败: {str(e)}")
            return False

    def launch_cursor(self) -> bool:
        """启动Cursor应用程序"""
        if not self.cursor_exe_path:
            print("⚠️  未找到Cursor可执行文件，无法自动启动")
            print("请手动启动Cursor")
            return False

        try:
            print(f"🚀 正在启动Cursor: {self.cursor_exe_path}")

            if sys.platform == "win32":
                # Windows: 使用subprocess.Popen以避免阻塞
                subprocess.Popen([self.cursor_exe_path],
                               creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)
            elif sys.platform == "darwin":
                # macOS: 使用open命令
                subprocess.Popen(["open", self.cursor_exe_path])
            else:
                # Linux: 直接启动
                subprocess.Popen([self.cursor_exe_path])

            print("✅ Cursor启动成功!")
            return True

        except Exception as e:
            print(f"❌ 启动Cursor失败: {str(e)}")
            print("请手动启动Cursor")
            return False

    def _ensure_cursor_closed(self):
        """确保Cursor进程已关闭"""
        import subprocess

        try:
            if sys.platform == "win32":
                # Windows: 检查并关闭Cursor进程
                result = subprocess.run(
                    ["tasklist", "/FI", "IMAGENAME eq Cursor.exe"],
                    capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW
                )
                if "Cursor.exe" in result.stdout:
                    print("🔄 检测到Cursor正在运行，正在关闭...")
                    subprocess.run(
                        ["taskkill", "/F", "/IM", "Cursor.exe"],
                        capture_output=True, creationflags=subprocess.CREATE_NO_WINDOW
                    )
                    import time
                    time.sleep(2)  # 等待进程完全关闭
                    print("✅ Cursor进程已关闭")
            elif sys.platform == "darwin":
                # macOS: 检查并关闭Cursor进程
                result = subprocess.run(["pgrep", "-f", "Cursor"], capture_output=True, text=True)
                if result.stdout.strip():
                    print("🔄 检测到Cursor正在运行，正在关闭...")
                    subprocess.run(["pkill", "-f", "Cursor"], capture_output=True)
                    import time
                    time.sleep(2)
                    print("✅ Cursor进程已关闭")
            else:
                # Linux: 检查并关闭Cursor进程
                result = subprocess.run(["pgrep", "-f", "cursor"], capture_output=True, text=True)
                if result.stdout.strip():
                    print("🔄 检测到Cursor正在运行，正在关闭...")
                    subprocess.run(["pkill", "-f", "cursor"], capture_output=True)
                    import time
                    time.sleep(2)
                    print("✅ Cursor进程已关闭")
        except Exception as e:
            print(f"⚠️ 无法自动关闭Cursor进程: {str(e)}")
            print("💡 请手动关闭Cursor后重试")


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Cursor Token Pusher - 简化版Token推送工具")
    print("=" * 60)
    print()
    
    # 创建推送器实例
    pusher = CursorTokenPusher()
    
    # 检查Cursor安装
    if not pusher.check_cursor_installation():
        input("\n按回车键退出...")
        return
    
    print(f"📍 Cursor数据库路径: {pusher.db_path}")
    print()
    
    # 获取用户输入
    print("📝 请输入以下信息:")
    print()
    
    # 获取Token
    while True:
        token = input("🔑 WorkosCursorSessionToken: ").strip()
        if token:
            break
        print("❌ Token不能为空，请重新输入")
    
    # 获取邮箱（可选）
    email = input("📧 邮箱地址 (可选，直接回车跳过): ").strip()
    if not email:
        email = None
    
    print()
    
    # 确认操作
    print("⚠️  注意: 此操作将修改Cursor的认证信息")
    confirm = input("是否继续? (y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("❌ 操作已取消")
        input("\n按回车键退出...")
        return
    
    print()
    
    # 备份数据库
    if not pusher.backup_database():
        print("⚠️  备份失败，但可以继续操作")
    
    print()
    
    # 推送Token
    if pusher.push_token(token, email):
        print()
        # 验证安装
        if pusher.verify_token_installation():
            print()
            print("🎉 Token推送成功!")
            print()

            # 询问是否启动Cursor
            launch_cursor = input("� 是否立即启动Cursor? (Y/n): ").strip().lower()
            if launch_cursor in ['', 'y', 'yes']:
                print()
                if pusher.launch_cursor():
                    print("�💡 提示: Cursor已启动，新的认证信息应该已生效")
                else:
                    print("💡 提示: 请手动启动Cursor以使更改生效")
            else:
                print("💡 提示:")
                print("   1. 请重启Cursor以使更改生效")
                print("   2. 如果遇到问题，可以使用备份文件恢复")
        else:
            print("⚠️  Token推送可能不完整，请检查")
    else:
        print("❌ Token推送失败")
    
    print()
    input("按回车键退出...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 操作被用户中断")
    except Exception as e:
        print(f"\n❌ 程序发生错误: {str(e)}")
        input("\n按回车键退出...")
