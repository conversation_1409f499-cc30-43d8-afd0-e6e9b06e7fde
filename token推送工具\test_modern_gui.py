#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代版GUI测试脚本
"""

import tkinter as tk
from cursor_token_pusher_gui_modern import ModernStyle

def test_modern_gui():
    """测试现代版GUI"""
    print("🎨 测试现代版GUI...")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("现代版GUI测试")
        root.geometry("500x400")
        root.configure(bg=ModernStyle.COLORS['background'])
        
        # 测试现代化组件
        title = tk.Label(root, 
                        text="🚀 现代化界面测试",
                        font=ModernStyle.FONTS['title'],
                        fg=ModernStyle.COLORS['text'],
                        bg=ModernStyle.COLORS['background'])
        title.pack(pady=20)
        
        # 测试卡片样式
        card = tk.Frame(root, 
                       bg=ModernStyle.COLORS['surface'],
                       relief='solid', 
                       bd=1)
        card.pack(fill=tk.X, padx=20, pady=10)
        
        card_content = tk.Frame(card, bg=ModernStyle.COLORS['surface'])
        card_content.pack(fill=tk.X, padx=20, pady=15)
        
        tk.Label(card_content,
                text="✅ 现代化卡片样式",
                font=ModernStyle.FONTS['heading'],
                fg=ModernStyle.COLORS['text'],
                bg=ModernStyle.COLORS['surface']).pack(anchor=tk.W)
        
        # 测试现代化按钮
        button = tk.Button(root,
                          text="🎨 现代化按钮",
                          font=ModernStyle.FONTS['body'],
                          bg=ModernStyle.COLORS['primary'],
                          fg='white',
                          relief='flat',
                          bd=0,
                          padx=30,
                          pady=12,
                          cursor='hand2')
        button.pack(pady=20)
        
        # 测试输入框
        entry = tk.Entry(root,
                        font=ModernStyle.FONTS['body'],
                        bg=ModernStyle.COLORS['surface'],
                        fg=ModernStyle.COLORS['text'],
                        relief='solid',
                        bd=2)
        entry.pack(pady=10, padx=20, fill=tk.X, ipady=8, ipadx=10)
        entry.insert(0, "现代化输入框测试")
        
        print("✅ 现代版GUI组件测试通过")
        
        # 显示窗口3秒后关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 现代版GUI测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎨 现代版GUI测试工具")
    print("=" * 40)
    
    if test_modern_gui():
        print("🎉 现代版GUI测试通过！")
        print("\n💡 可以运行现代版GUI:")
        print("   python cursor_token_pusher_gui_modern.py")
        print("   或者: run_modern_gui.bat")
    else:
        print("❌ 现代版GUI测试失败")

if __name__ == "__main__":
    main()
