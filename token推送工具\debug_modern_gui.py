#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试现代化GUI程序
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def debug_modern_gui():
    try:
        print("开始调试现代化GUI...")
        
        # 尝试导入现代化GUI
        print("正在导入cursor_token_pusher_gui_modern...")
        from cursor_token_pusher_gui_modern import CursorTokenPusherModernGUI, ModernStyle
        print("✅ 导入成功")
        
        # 尝试导入核心功能
        print("正在导入cursor_token_pusher...")
        from cursor_token_pusher import CursorTokenPusher
        print("✅ 核心功能导入成功")
        
        # 创建根窗口
        print("正在创建根窗口...")
        root = tk.Tk()
        print("✅ 根窗口创建成功")
        
        # 创建应用实例
        print("正在创建应用实例...")
        app = CursorTokenPusherModernGUI(root)
        print("✅ 应用实例创建成功")
        
        # 检查按钮是否存在
        if hasattr(app, 'push_button'):
            print("✅ 推送按钮存在")
            print(f"按钮文本: {app.push_button.cget('text')}")
        else:
            print("❌ 推送按钮不存在")
        
        print("正在启动GUI...")
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {str(e)}")
        import traceback
        traceback.print_exc()
    except Exception as e:
        print(f"❌ 其他错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_modern_gui()
