#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Token Pusher - 图形界面版本
使用Tkinter创建的用户友好界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import sys
import os

# 导入核心功能
from cursor_token_pusher import CursorTokenPusher


class CursorTokenPusherGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Cursor Token Pusher - 图形界面版")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 设置图标（如果有的话）
        try:
            self.root.iconbitmap("cursor.ico")
        except:
            pass
        
        self.pusher = CursorTokenPusher()
        self.setup_ui()
        self.check_cursor_status()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🚀 Cursor Token Pusher", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Cursor状态显示
        status_frame = ttk.LabelFrame(main_frame, text="Cursor 状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="数据库:").grid(row=0, column=0, sticky=tk.W)
        self.db_status_label = ttk.Label(status_frame, text="检查中...")
        self.db_status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="可执行文件:").grid(row=1, column=0, sticky=tk.W)
        self.exe_status_label = ttk.Label(status_frame, text="检查中...")
        self.exe_status_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # Token输入区域
        token_frame = ttk.LabelFrame(main_frame, text="Token 信息", padding="10")
        token_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        token_frame.columnconfigure(1, weight=1)

        # 重要提示
        warning_label = ttk.Label(token_frame,
                                 text="⚠️ 推送前请先：1) 退出Cursor账号  2) 完全关闭Cursor",
                                 font=("Arial", 9),
                                 foreground="red")
        warning_label.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        ttk.Label(token_frame, text="WorkosCursorSessionToken:").grid(row=1, column=0, sticky=tk.W)
        self.token_entry = ttk.Entry(token_frame, width=50, show="*")
        self.token_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # 显示/隐藏Token按钮
        self.show_token_var = tk.BooleanVar()
        self.show_token_check = ttk.Checkbutton(token_frame, text="显示Token",
                                               variable=self.show_token_var,
                                               command=self.toggle_token_visibility)
        self.show_token_check.grid(row=1, column=2, padx=(10, 0))
        
        # 移除邮箱输入部分

        # Cursor路径配置区域
        path_frame = ttk.LabelFrame(main_frame, text="Cursor 路径配置", padding="10")
        path_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        path_frame.columnconfigure(1, weight=1)

        ttk.Label(path_frame, text="Cursor可执行文件路径:").grid(row=0, column=0, sticky=tk.W)

        # 路径输入框和按钮的容器
        path_input_frame = ttk.Frame(path_frame)
        path_input_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        path_input_frame.columnconfigure(0, weight=1)

        self.cursor_path_entry = ttk.Entry(path_input_frame, width=50)
        self.cursor_path_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))

        ttk.Button(path_input_frame, text="浏览",
                  command=self.browse_cursor_path).grid(row=0, column=1, padx=(5, 0))

        ttk.Button(path_input_frame, text="自动检测",
                  command=self.auto_detect_cursor_path).grid(row=0, column=2, padx=(5, 0))

        # 说明文字
        ttk.Label(path_frame, text="例如: D:\\cursor\\Cursor.exe",
                 font=("Arial", 8)).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))

        # 预填充检测到的路径
        if self.pusher.cursor_exe_path:
            self.cursor_path_entry.insert(0, self.pusher.cursor_exe_path)

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(0, 10))
        
        self.push_button = ttk.Button(button_frame, text="🚀 推送Token", 
                                     command=self.push_token, style="Accent.TButton")
        self.push_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.backup_button = ttk.Button(button_frame, text="💾 备份数据库", 
                                       command=self.backup_database)
        self.backup_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_button = ttk.Button(button_frame, text="🔍 测试连接", 
                                     command=self.test_connection)
        self.test_button.pack(side=tk.LEFT)
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        ttk.Button(bottom_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(bottom_frame, text="配置帮助", command=self.show_help).pack(side=tk.RIGHT, padx=(0, 5))
        ttk.Button(bottom_frame, text="关于", command=self.show_about).pack(side=tk.RIGHT)
    
    def toggle_token_visibility(self):
        """切换Token显示/隐藏"""
        if self.show_token_var.get():
            self.token_entry.config(show="")
        else:
            self.token_entry.config(show="*")

    def browse_cursor_path(self):
        """浏览选择Cursor可执行文件"""
        if sys.platform == "win32":
            filetypes = [("可执行文件", "*.exe"), ("所有文件", "*.*")]
            title = "选择Cursor.exe文件"
        elif sys.platform == "darwin":
            filetypes = [("应用程序", "*.app"), ("所有文件", "*.*")]
            title = "选择Cursor应用程序"
        else:
            filetypes = [("所有文件", "*.*")]
            title = "选择Cursor可执行文件"

        filename = filedialog.askopenfilename(
            title=title,
            filetypes=filetypes
        )

        if filename:
            self.cursor_path_entry.delete(0, tk.END)
            self.cursor_path_entry.insert(0, filename)
            self.log(f"📁 已选择Cursor路径: {filename}")

    def auto_detect_cursor_path(self):
        """自动检测Cursor路径"""
        def detect():
            self.log("🔍 正在自动检测Cursor路径...")

            # 重新创建pusher实例以重新检测
            from cursor_token_pusher import CursorTokenPusher
            temp_pusher = CursorTokenPusher()

            if temp_pusher.cursor_exe_path:
                self.cursor_path_entry.delete(0, tk.END)
                self.cursor_path_entry.insert(0, temp_pusher.cursor_exe_path)
                self.log(f"✅ 自动检测成功: {temp_pusher.cursor_exe_path}")

                # 更新当前pusher的路径
                self.pusher.cursor_exe_path = temp_pusher.cursor_exe_path
            else:
                self.log("❌ 自动检测失败，请手动选择Cursor路径")
                messagebox.showwarning("检测失败", "未能自动检测到Cursor路径，请手动选择")

        threading.Thread(target=detect, daemon=True).start()
    
    def log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def check_cursor_status(self):
        """检查Cursor状态"""
        def check():
            # 检查数据库
            if self.pusher.check_cursor_installation():
                self.db_status_label.config(text="✅ 已找到", foreground="green")
            else:
                self.db_status_label.config(text="❌ 未找到", foreground="red")
            
            # 检查可执行文件
            if self.pusher.cursor_exe_path:
                self.exe_status_label.config(text=f"✅ {self.pusher.cursor_exe_path}", foreground="green")
            else:
                self.exe_status_label.config(text="❌ 未找到", foreground="red")
        
        # 在后台线程中检查
        threading.Thread(target=check, daemon=True).start()
    
    def backup_database(self):
        """备份数据库"""
        def backup():
            self.log("🔄 正在备份数据库...")
            if self.pusher.backup_database():
                self.log("✅ 数据库备份成功")
                messagebox.showinfo("成功", "数据库备份成功！")
            else:
                self.log("❌ 数据库备份失败")
                messagebox.showerror("错误", "数据库备份失败！")
        
        threading.Thread(target=backup, daemon=True).start()
    
    def test_connection(self):
        """测试连接"""
        def test():
            self.log("🔍 正在测试Cursor连接...")
            if self.pusher.verify_token_installation():
                self.log("✅ Cursor连接正常")
                messagebox.showinfo("测试结果", "Cursor连接正常！")
            else:
                self.log("⚠️ Cursor连接异常或未配置Token")
                messagebox.showwarning("测试结果", "Cursor连接异常或未配置Token")
        
        threading.Thread(target=test, daemon=True).start()
    
    def push_token(self):
        """推送Token"""
        token = self.token_entry.get().strip()
        cursor_path = self.cursor_path_entry.get().strip()
        email = None  # 不再使用邮箱

        if not token:
            messagebox.showerror("错误", "请输入WorkosCursorSessionToken！")
            return

        # 确认用户已经退出账号并关闭Cursor
        confirm_msg = """推送前确认检查：

1. 是否已经退出Cursor中的当前账号？
2. 是否已经完全关闭Cursor程序？

如果没有完成以上步骤，推送可能会失败或不生效。

确认继续推送吗？"""
        if not messagebox.askyesno("推送确认", confirm_msg):
            self.log("❌ 用户取消推送操作")
            return

        # 验证Cursor路径
        if cursor_path:
            if not os.path.exists(cursor_path):
                self.log("❌ 指定的Cursor路径不存在")
                messagebox.showerror("错误", f"指定的Cursor路径不存在：\n{cursor_path}")
                return
            # 更新pusher的Cursor路径
            self.pusher.cursor_exe_path = cursor_path
            self.log(f"📁 使用指定的Cursor路径: {cursor_path}")
        elif not self.pusher.cursor_exe_path:
            self.log("⚠️ 未指定Cursor路径，推送后需要手动启动Cursor")
        
        # 禁用按钮防止重复点击
        self.push_button.config(state="disabled")
        
        def push():
            try:
                self.log("🚀 开始推送Token...")
                self.log(f"🔑 Token: {token[:20]}...")
                
                # 推送Token
                if self.pusher.push_token(token, email):
                    self.log("✅ Token推送成功！")
                    
                    # 验证安装
                    if self.pusher.verify_token_installation():
                        self.log("✅ Token验证通过")
                        
                        # 询问是否启动Cursor
                        result = messagebox.askyesno("推送成功", 
                                                   "Token推送成功！\n\n是否立即启动Cursor？")
                        if result:
                            self.log("🚀 正在启动Cursor...")
                            if self.pusher.launch_cursor():
                                self.log("✅ Cursor启动成功！")
                                messagebox.showinfo("完成", "Cursor已启动，新的认证信息应该已生效！")
                            else:
                                self.log("❌ Cursor启动失败，请手动启动")
                                messagebox.showwarning("提示", "Cursor启动失败，请手动启动Cursor")
                        else:
                            messagebox.showinfo("完成", "Token推送完成！请重启Cursor以使更改生效。")
                    else:
                        self.log("⚠️ Token验证失败")
                        messagebox.showwarning("警告", "Token推送可能不完整，请检查！")
                else:
                    self.log("❌ Token推送失败")
                    messagebox.showerror("错误", "Token推送失败！")
                    
            except Exception as e:
                self.log(f"❌ 发生错误: {str(e)}")
                messagebox.showerror("错误", f"发生错误：{str(e)}")
            finally:
                # 重新启用按钮
                self.push_button.config(state="normal")
        
        threading.Thread(target=push, daemon=True).start()
    
    def show_help(self):
        """显示配置帮助"""
        help_text = """
Cursor Token Pusher - 配置帮助

主要配置项：
• Token: 必需，输入你的WorkosCursorSessionToken
• Cursor路径: 可选，通常可以自动检测

Cursor路径配置：
• 点击"自动检测" - 自动查找Cursor安装位置
• 点击"浏览" - 手动选择Cursor.exe文件
• 直接输入完整路径

Windows常见路径：
• D:\\cursor\\Cursor.exe
• C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe
• C:\\Program Files\\Cursor\\Cursor.exe

常见问题：
• 如果自动检测失败，请手动选择Cursor路径
• 推送前请确保Cursor完全关闭
• 如遇权限问题，请以管理员身份运行

提示：
• 数据库路径通常无需配置，程序自动处理
• 详细帮助请查看"环境配置帮助.md"文件
"""
        messagebox.showinfo("配置帮助", help_text)

    def show_about(self):
        """显示关于信息"""
        about_text = """
Cursor Token Pusher - 图形界面版

功能：
• 直接推送WorkosCursorSessionToken到Cursor
• 智能Cursor路径检测和配置
• 自动备份数据库
• 自动启动Cursor
• 跨平台支持

使用方法：
1. 输入有效的WorkosCursorSessionToken
2. 配置Cursor可执行文件路径（可自动检测或手动选择）
3. 点击"推送Token"按钮
4. 选择是否自动启动Cursor

注意事项：
• 推送前请确保Cursor已完全关闭
• 工具会自动备份原数据库文件
• 如遇问题可使用备份文件恢复

版本: 2.0
"""
        messagebox.showinfo("关于", about_text)


def main():
    """主函数"""
    root = tk.Tk()
    
    # 设置主题（如果支持）
    try:
        style = ttk.Style()
        style.theme_use('clam')  # 使用更现代的主题
    except:
        pass
    
    app = CursorTokenPusherGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        root.quit()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 启动GUI
    root.mainloop()


if __name__ == "__main__":
    main()
