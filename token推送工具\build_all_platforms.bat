@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 Cursor Token Pusher 跨平台打包工具
echo ========================================
echo.

echo 📋 当前支持的平台:
echo    - Windows (当前平台)
echo    - macOS (需要在Mac上运行)
echo    - Linux (需要在Linux上运行)
echo.

echo ⚠️  注意: 此脚本只能在Windows上打包Windows版本
echo    要打包其他平台版本，请在对应平台上运行相应脚本
echo.

pause

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.6+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 📦 开始Windows平台打包...
python build_cross_platform.py

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo 🎉 Windows平台打包完成！
echo.
echo 📁 输出文件:
dir /b CursorTokenPusher_v2.0_Windows.*
echo.

echo 💡 其他平台打包说明:
echo.
echo 📱 macOS平台:
echo    1. 将项目文件复制到Mac电脑
echo    2. 在终端中运行: python3 build_cross_platform.py
echo    3. 输出: CursorTokenPusher_v2.0_macOS.zip
echo.
echo 🐧 Linux平台:
echo    1. 将项目文件复制到Linux电脑
echo    2. 在终端中运行: python3 build_cross_platform.py
echo    3. 输出: CursorTokenPusher_v2.0_Linux.zip
echo.

echo ✨ 打包完成！
pause
