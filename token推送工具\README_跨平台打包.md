# Cursor Token Pusher - 跨平台打包指南

## 🎯 概述

本项目现在支持跨平台打包，可以生成Windows、macOS和Linux三个平台的可执行文件。

## 📁 项目结构

```
token推送工具/
├── cursor_token_pusher_gui_modern.py    # 现代化GUI主程序
├── cursor_token_pusher.py               # 核心Token推送逻辑
├── cursor_config.py                     # 平台配置文件
├── build_cross_platform.py             # 跨平台打包脚本
├── build_all_platforms.bat             # Windows批处理脚本
├── build_all_platforms.sh              # Linux/macOS Shell脚本
├── 跨平台使用说明.md                    # 详细使用说明
└── README_跨平台打包.md                 # 本文件
```

## 🚀 快速开始

### Windows用户

1. **双击运行批处理文件**
   ```
   双击 build_all_platforms.bat
   ```

2. **或使用命令行**
   ```cmd
   cd token推送工具
   python build_cross_platform.py
   ```

### macOS用户

1. **使用Shell脚本**
   ```bash
   cd token推送工具
   chmod +x build_all_platforms.sh
   ./build_all_platforms.sh
   ```

2. **或直接运行Python脚本**
   ```bash
   python3 build_cross_platform.py
   ```

### Linux用户

1. **使用Shell脚本**
   ```bash
   cd token推送工具
   chmod +x build_all_platforms.sh
   ./build_all_platforms.sh
   ```

2. **或直接运行Python脚本**
   ```bash
   python3 build_cross_platform.py
   ```

## 📦 打包输出

每个平台的打包会生成以下文件：

### Windows
- `CursorTokenPusher_v2.0_Windows.zip` - 完整发布包
- `CursorTokenPusher_v2.0_Windows/` - 解压后的文件夹
  - `CursorTokenPusher.exe` - 可执行文件
  - `README.txt` - 使用说明
  - 其他文档文件

### macOS
- `CursorTokenPusher_v2.0_macOS.zip` - 完整发布包
- `CursorTokenPusher_v2.0_macOS/` - 解压后的文件夹
  - `CursorTokenPusher.app` - 应用程序包
  - `README.txt` - 使用说明
  - 其他文档文件

### Linux
- `CursorTokenPusher_v2.0_Linux.zip` - 完整发布包
- `CursorTokenPusher_v2.0_Linux/` - 解压后的文件夹
  - `CursorTokenPusher` - 可执行文件
  - `README.txt` - 使用说明
  - 其他文档文件

## 🔧 环境要求

### 通用要求
- Python 3.6 或更高版本
- PyInstaller (自动安装)

### Windows
- Windows 7 或更高版本
- 推荐使用 Python 3.8+

### macOS
- macOS 10.12 或更高版本
- Xcode Command Line Tools (通常已安装)
- 推荐使用 Python 3.8+

### Linux
- 主要Linux发行版 (Ubuntu, CentOS, Debian等)
- 必要的系统库 (通常已预装)
- 推荐使用 Python 3.8+

## 🎨 功能特性

### 跨平台支持
- ✅ Windows 7/8/10/11
- ✅ macOS 10.12+ (Intel & Apple Silicon)
- ✅ Linux (主要发行版)

### 自动化打包
- 🔄 自动检测当前平台
- 📦 生成对应平台的可执行文件
- 🗜️ 自动创建发布ZIP包
- 📋 生成平台特定的README

### 智能路径检测
- 🔍 自动检测Cursor安装路径
- 🛠️ 支持手动配置路径
- 📁 跨平台数据库路径处理

## ⚠️ 注意事项

### 打包限制
- 每个平台只能在对应的操作系统上打包
- Windows版本需要在Windows上打包
- macOS版本需要在Mac上打包
- Linux版本需要在Linux上打包

### 分发建议
1. **Windows用户**：分发 `.exe` 文件
2. **macOS用户**：分发 `.app` 应用程序包
3. **Linux用户**：分发可执行文件，注意设置执行权限

### 兼容性
- 在较新系统上打包的程序可能无法在较旧系统上运行
- 建议在目标系统的最低版本上进行打包

## 🐛 故障排除

### 常见问题

1. **PyInstaller安装失败**
   ```bash
   # 升级pip
   python -m pip install --upgrade pip
   # 重新安装PyInstaller
   pip install pyinstaller
   ```

2. **打包过程中出错**
   - 检查Python版本是否符合要求
   - 确保所有必要文件都存在
   - 查看详细错误信息

3. **生成的程序无法运行**
   - 检查目标系统兼容性
   - 确认所有依赖都已包含
   - 在目标系统上测试

### 获取帮助

如果遇到问题，请提供以下信息：
- 操作系统版本
- Python版本
- 完整的错误信息
- 详细的操作步骤

**联系方式**：
- 作者：kkk
- 邮箱：<EMAIL>

## 📈 版本历史

### v2.0 (当前版本)
- ✨ 新增跨平台支持
- 🔄 重构打包脚本
- 📱 现代化GUI界面
- 🛠️ 智能路径检测
- 📦 自动化打包流程

### v1.x
- 🎯 基础Windows支持
- 💻 命令行界面
- 🔧 手动配置

## 🚀 未来计划

- [ ] 支持更多Linux发行版
- [ ] 添加自动更新功能
- [ ] 优化打包体积
- [ ] 增加更多配置选项
- [ ] 支持批量Token管理

## 📄 许可证

© 2024 kkk. All rights reserved.

---

**感谢使用 Cursor Token Pusher！** 🎉
